# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-12-19

### Added
- 🎉 Initial release of convert-apps-to-homebrew
- 🔍 Automatic application discovery from /Applications directory
- 📦 Smart categorization of apps (cask/formula/unavailable/already-installed)
- ✅ Interactive checkbox selection with all apps pre-selected by default
- 🔐 Secure sudo password handling for cask installations
- 🧪 Comprehensive dry-run mode for previewing changes
- 📊 Detailed reporting and progress tracking
- 🚫 Flexible app filtering with --ignore flag
- 🎨 Beautiful colorized interface with emojis and progress indicators
- ⚡ Efficient batch installation of multiple packages
- 🛡️ Comprehensive error handling with recovery suggestions
- 📝 Verbose logging mode for debugging
- 🔧 Custom applications directory support
- 🧪 Comprehensive test suite with 123 tests and 74% coverage
- 📖 Complete documentation and usage examples

### Features
- **CLI Interface**: Commander.js-based command-line interface
- **Interactive Prompts**: Inquirer.js checkbox and password prompts
- **App Discovery**: Scans /Applications and matches with Homebrew packages
- **Batch Installation**: Installs multiple casks and formulas efficiently
- **Security**: Secure password handling and file operations
- **Error Handling**: Context-aware error messages with recovery suggestions
- **Progress Tracking**: Real-time progress indicators for long operations
- **Dry-Run Mode**: Preview mode that shows what would happen without executing
- **Flexible Configuration**: Support for ignored apps and custom directories

### Technical Details
- **TypeScript**: Fully written in TypeScript with comprehensive type definitions
- **Node.js 22+**: Requires modern Node.js for optimal performance
- **macOS Only**: Designed specifically for macOS environments
- **Homebrew Integration**: Direct integration with Homebrew commands
- **Test Coverage**: 123 tests across 9 test suites with excellent coverage
- **Build System**: TypeScript compilation with source maps
- **Package Management**: Proper npm package configuration for global installation

### Dependencies
- **Runtime Dependencies**:
  - @inquirer/checkbox: Interactive multi-select prompts
  - @inquirer/password: Secure password input
  - commander: Command-line interface framework
- **Development Dependencies**:
  - TypeScript: Type-safe development
  - Jest: Comprehensive testing framework
  - @types packages: TypeScript type definitions

### Documentation
- **README.md**: Comprehensive usage guide with examples
- **TEST_COVERAGE.md**: Detailed test coverage analysis
- **CHANGELOG.md**: Version history and changes
- **LICENSE**: MIT license for open-source usage

### Installation Methods
- **npx**: Direct execution without installation
- **Global npm**: Install globally for repeated use
- **Local development**: Clone and build from source

### Command Line Options
- `--ignore <apps...>`: Ignore specific applications
- `--dry-run`: Preview changes without executing
- `--verbose`: Enable detailed logging
- `--applications-dir <path>`: Custom applications directory
- `--help`: Show help information
- `--version`: Show version number

### Supported Workflows
- **Discovery**: Automatic app scanning and categorization
- **Selection**: Interactive checkbox interface for app selection
- **Installation**: Batch installation with progress tracking
- **Cleanup**: Optional removal of original app files for casks
- **Reporting**: Comprehensive success/failure summaries

### Error Handling
- **Homebrew Not Installed**: Clear installation instructions
- **Permission Errors**: Guidance for file access and sudo requirements
- **Network Issues**: Troubleshooting for connectivity problems
- **Command Failures**: Homebrew-specific troubleshooting steps
- **File System Errors**: Directory and path validation guidance
- **Input Validation**: Command-line argument formatting help

### Quality Assurance
- **Comprehensive Testing**: Unit tests, integration tests, and mocking
- **TypeScript Strict Mode**: Full type safety and error prevention
- **Code Coverage**: 74% statement coverage with focus on critical paths
- **Error Recovery**: Graceful handling of all error scenarios
- **User Experience**: Professional interface with clear feedback

### Future Compatibility
- **Extensible Architecture**: Modular design for future enhancements
- **Version Management**: Semantic versioning for stable releases
- **Backward Compatibility**: Commitment to maintaining API stability
- **Community Contributions**: Open-source model for community involvement

---

## Release Notes

### v1.0.0 - Initial Release

This is the first stable release of convert-apps-to-homebrew, a modern TypeScript CLI tool for converting manually installed macOS applications to Homebrew-managed installations.

**Key Highlights:**
- Complete rewrite of the original shell script in TypeScript
- Interactive user interface with checkbox selection
- Comprehensive error handling and recovery suggestions
- Dry-run mode for safe preview of changes
- Batch installation for improved efficiency
- Extensive test coverage for reliability

**Migration from Shell Script:**
This TypeScript version provides significant improvements over the original shell script:
- Better error handling and user feedback
- Interactive selection instead of manual editing
- Progress tracking and status indicators
- Comprehensive testing and reliability
- Modern development practices and maintainability

**Getting Started:**
```bash
npx convert-apps-to-homebrew
```

For detailed usage instructions, see the [README.md](README.md) file.

**Support:**
- Node.js 22+ required
- macOS only
- Homebrew must be installed

**Contributing:**
We welcome contributions! Please see the README.md for development guidelines and contribution instructions.
