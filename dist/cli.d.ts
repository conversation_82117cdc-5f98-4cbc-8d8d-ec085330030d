import { Command } from 'commander';
import { CommandOptions } from './types';
export declare function createProgram(): Command;
export declare function parseArguments(argv?: string[]): CommandOptions;
export declare function displayWelcome(options: CommandOptions): void;
export declare function displayTroubleshooting(): void;
export declare function setupSignalHandlers(): void;
export declare function validateEnvironment(): void;
//# sourceMappingURL=cli.d.ts.map