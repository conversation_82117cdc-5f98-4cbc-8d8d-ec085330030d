"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.REGEX_PATTERNS = exports.MESSAGES = exports.COLORS = exports.DEFAULT_CONFIG = exports.EXIT_CODES = exports.FILE_PATTERNS = exports.BREW_COMMANDS = exports.DEFAULT_APPLICATIONS_DIR = void 0;
exports.DEFAULT_APPLICATIONS_DIR = '/Applications';
exports.BREW_COMMANDS = {
    LIST_CASKS: 'brew ls -1 --cask',
    LIST_FORMULAS: 'brew leaves',
    INFO_CASK: (name) => `brew info --cask "${name}"`,
    INFO_FORMULA: (name) => `brew info "${name}"`,
    INSTALL_CASK: (names) => `brew install --cask ${names.map(n => `"${n}"`).join(' ')}`,
    INSTALL_FORMULA: (names) => `brew install ${names.map(n => `"${n}"`).join(' ')}`,
    VERSION: 'brew --version'
};
exports.FILE_PATTERNS = {
    APP_EXTENSION: '.app',
    APP_PATTERN: /\.app$/i
};
exports.EXIT_CODES = {
    SUCCESS: 0,
    GENERAL_ERROR: 1,
    HOMEBREW_NOT_INSTALLED: 2,
    PERMISSION_DENIED: 3,
    INVALID_INPUT: 4,
    NETWORK_ERROR: 5
};
exports.DEFAULT_CONFIG = {
    MAX_CONCURRENT_OPERATIONS: 5,
    BREW_COMMAND_TIMEOUT: 30000,
    VERBOSE: false,
    DRY_RUN: false
};
exports.COLORS = {
    RESET: '\x1b[0m',
    BRIGHT: '\x1b[1m',
    DIM: '\x1b[2m',
    RED: '\x1b[31m',
    GREEN: '\x1b[32m',
    YELLOW: '\x1b[33m',
    BLUE: '\x1b[34m',
    MAGENTA: '\x1b[35m',
    CYAN: '\x1b[36m',
    WHITE: '\x1b[37m'
};
exports.MESSAGES = {
    HOMEBREW_NOT_INSTALLED: 'Homebrew is not installed. Please install it before continuing.',
    PERMISSION_DENIED: 'Permission denied. You may need to run with appropriate permissions.',
    NO_APPS_FOUND: 'No applications found in the Applications directory.',
    NO_APPS_SELECTED: 'No applications selected for installation.',
    DRY_RUN_MODE: 'Running in dry-run mode. No actual changes will be made.',
    OPERATION_CANCELLED: 'Operation cancelled by user.',
    SCANNING_APPS: 'Scanning applications...',
    CHECKING_HOMEBREW: 'Checking Homebrew availability...',
    INSTALLING_PACKAGES: 'Installing packages...',
    DELETING_APPS: 'Deleting original applications...',
    OPERATION_COMPLETE: 'Operation completed successfully.'
};
exports.REGEX_PATTERNS = {
    BREW_PACKAGE_NAME: /^[a-z0-9][a-z0-9\-_.]*$/i,
    APP_NAME: /^[^\/\0]+$/,
    VERSION: /^\d+\.\d+\.\d+/
};
//# sourceMappingURL=constants.js.map