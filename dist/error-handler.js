"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProgressTracker = exports.ErrorHandler = void 0;
exports.initializeErrorHandler = initializeErrorHandler;
exports.getErrorHandler = getErrorHandler;
exports.setupGlobalErrorHandlers = setupGlobalErrorHandlers;
exports.createProgressCallback = createProgressCallback;
const types_1 = require("./types");
const constants_1 = require("./constants");
const utils_1 = require("./utils");
class ErrorHandler {
    logger;
    verbose;
    constructor(verbose = false) {
        this.logger = (0, utils_1.createLogger)(verbose);
        this.verbose = verbose;
    }
    handleError(error, context) {
        const contextMsg = context ? ` (${context})` : '';
        if (error instanceof types_1.ConvertAppsError) {
            this.handleConvertAppsError(error, contextMsg);
        }
        else {
            this.handleGenericError(error, contextMsg);
        }
    }
    handleConvertAppsError(error, context) {
        switch (error.type) {
            case types_1.ErrorType.HOMEBREW_NOT_INSTALLED:
                this.logger.error(`${constants_1.MESSAGES.HOMEBREW_NOT_INSTALLED}${context}`);
                this.showHomebrewInstallationHelp();
                process.exit(constants_1.EXIT_CODES.HOMEBREW_NOT_INSTALLED);
            case types_1.ErrorType.PERMISSION_DENIED:
                this.logger.error(`${constants_1.MESSAGES.PERMISSION_DENIED}${context}`);
                this.showPermissionHelp();
                process.exit(constants_1.EXIT_CODES.PERMISSION_DENIED);
            case types_1.ErrorType.NETWORK_ERROR:
                this.logger.error(`Network error occurred${context}. Please check your internet connection.`);
                this.showNetworkHelp();
                process.exit(constants_1.EXIT_CODES.NETWORK_ERROR);
            case types_1.ErrorType.COMMAND_FAILED:
                this.logger.error(`Command execution failed${context}: ${error.message}`);
                this.showCommandFailureHelp();
                process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
            case types_1.ErrorType.FILE_NOT_FOUND:
                this.logger.error(`File not found${context}: ${error.message}`);
                this.showFileNotFoundHelp();
                process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
            case types_1.ErrorType.INVALID_INPUT:
                this.logger.error(`Invalid input${context}: ${error.message}`);
                this.showInputValidationHelp();
                process.exit(constants_1.EXIT_CODES.INVALID_INPUT);
            default:
                this.logger.error(`Application error${context}: ${error.message}`);
                if (error.originalError && this.verbose) {
                    this.logger.debug(`Original error: ${error.originalError.message}`);
                }
                process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
        }
    }
    handleGenericError(error, context) {
        this.logger.error(`Unexpected error${context}: ${error.message}`);
        if (error.message.includes('ENOENT')) {
            this.showFileNotFoundHelp();
        }
        else if (error.message.includes('EACCES')) {
            this.showPermissionHelp();
        }
        else if (error.message.includes('ENOTDIR')) {
            this.logger.info('💡 The specified path is not a directory. Check your --applications-dir setting.');
        }
        else if (error.message.includes('spawn') && error.message.includes('ENOENT')) {
            this.logger.info('💡 Command not found. Make sure Homebrew is installed and in your PATH.');
        }
        if (this.verbose) {
            this.logger.debug(`Stack trace: ${error.stack}`);
        }
        process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
    }
    showHomebrewInstallationHelp() {
        console.log((0, utils_1.colorize)('\n🍺 Homebrew Installation Help:', 'CYAN'));
        console.log('1. Install Homebrew: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"');
        console.log('2. Add Homebrew to your PATH (follow the installation instructions)');
        console.log('3. Verify installation: brew --version');
        console.log('4. Run this tool again');
        console.log('\nFor more information: https://brew.sh/');
    }
    showPermissionHelp() {
        console.log((0, utils_1.colorize)('\n🔐 Permission Help:', 'CYAN'));
        console.log('1. Make sure you have read access to /Applications directory');
        console.log('2. For cask installations, you need admin privileges to delete original apps');
        console.log('3. Try running: sudo chown -R $(whoami) /usr/local/Homebrew');
        console.log('4. Or run this tool with appropriate permissions');
    }
    showNetworkHelp() {
        console.log((0, utils_1.colorize)('\n🌐 Network Help:', 'CYAN'));
        console.log('1. Check your internet connection');
        console.log('2. Verify DNS resolution: nslookup github.com');
        console.log('3. Check if you\'re behind a corporate firewall');
        console.log('4. Try again in a few minutes');
    }
    showCommandFailureHelp() {
        console.log((0, utils_1.colorize)('\n⚙️  Command Failure Help:', 'CYAN'));
        console.log('1. Check if Homebrew is working: brew doctor');
        console.log('2. Update Homebrew: brew update');
        console.log('3. Check available disk space');
        console.log('4. Try running with --verbose for more details');
    }
    showFileNotFoundHelp() {
        console.log((0, utils_1.colorize)('\n📁 File Not Found Help:', 'CYAN'));
        console.log('1. Check if /Applications directory exists');
        console.log('2. Verify the specified applications directory path');
        console.log('3. Make sure you have read permissions');
        console.log('4. Try using --applications-dir to specify a different path');
    }
    showInputValidationHelp() {
        console.log((0, utils_1.colorize)('\n📝 Input Validation Help:', 'CYAN'));
        console.log('1. Check your command line arguments');
        console.log('2. App names in --ignore should not be empty or whitespace');
        console.log('3. Use quotes for app names with spaces: --ignore "Adobe Photoshop"');
        console.log('4. Use --help to see all available options');
    }
}
exports.ErrorHandler = ErrorHandler;
class ProgressTracker {
    logger;
    startTime;
    lastUpdate;
    constructor(verbose = false) {
        this.logger = (0, utils_1.createLogger)(verbose);
        this.startTime = Date.now();
        this.lastUpdate = this.startTime;
    }
    startOperation(operation, total) {
        this.startTime = Date.now();
        this.lastUpdate = this.startTime;
        if (total !== undefined) {
            this.logger.info(`🚀 Starting ${operation} (${total} items)...`);
        }
        else {
            this.logger.info(`🚀 Starting ${operation}...`);
        }
    }
    updateProgress(message, current, total) {
        const now = Date.now();
        const elapsed = now - this.startTime;
        const sinceLastUpdate = now - this.lastUpdate;
        if (sinceLastUpdate < 1000 && current !== total) {
            return;
        }
        this.lastUpdate = now;
        let progressMsg = message;
        if (current !== undefined && total !== undefined) {
            const percentage = Math.round((current / total) * 100);
            const progressBar = this.createProgressBar(current, total);
            progressMsg = `${message} ${progressBar} ${current}/${total} (${percentage}%)`;
        }
        if (elapsed > 5000) {
            const elapsedSeconds = Math.round(elapsed / 1000);
            progressMsg += ` [${elapsedSeconds}s]`;
        }
        this.logger.info(progressMsg);
    }
    completeOperation(operation, success = true) {
        const elapsed = Date.now() - this.startTime;
        const elapsedSeconds = (elapsed / 1000).toFixed(1);
        if (success) {
            this.logger.info(`✅ ${operation} completed in ${elapsedSeconds}s`);
        }
        else {
            this.logger.warn(`⚠️  ${operation} completed with errors in ${elapsedSeconds}s`);
        }
    }
    createProgressBar(current, total, width = 20) {
        const percentage = current / total;
        const filled = Math.round(width * percentage);
        const empty = width - filled;
        return `[${'█'.repeat(filled)}${'░'.repeat(empty)}]`;
    }
}
exports.ProgressTracker = ProgressTracker;
let globalErrorHandler = null;
function initializeErrorHandler(verbose = false) {
    globalErrorHandler = new ErrorHandler(verbose);
    return globalErrorHandler;
}
function getErrorHandler() {
    if (!globalErrorHandler) {
        globalErrorHandler = new ErrorHandler();
    }
    return globalErrorHandler;
}
function setupGlobalErrorHandlers(verbose = false) {
    const errorHandler = initializeErrorHandler(verbose);
    process.on('uncaughtException', (error) => {
        console.error((0, utils_1.colorize)('\n💥 Uncaught Exception:', 'RED'));
        errorHandler.handleError(error, 'uncaught exception');
    });
    process.on('unhandledRejection', (reason) => {
        console.error((0, utils_1.colorize)('\n💥 Unhandled Promise Rejection:', 'RED'));
        const error = reason instanceof Error ? reason : new Error(String(reason));
        errorHandler.handleError(error, 'unhandled rejection');
    });
}
function createProgressCallback(tracker) {
    return (message, current, total) => {
        tracker.updateProgress(message, current, total);
    };
}
//# sourceMappingURL=error-handler.js.map