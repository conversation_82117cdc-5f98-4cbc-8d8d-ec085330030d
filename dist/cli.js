"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createProgram = createProgram;
exports.parseArguments = parseArguments;
exports.displayWelcome = displayWelcome;
exports.displayTroubleshooting = displayTroubleshooting;
exports.setupSignalHandlers = setupSignalHandlers;
exports.validateEnvironment = validateEnvironment;
const commander_1 = require("commander");
const fs_1 = require("fs");
const path_1 = require("path");
const utils_1 = require("./utils");
const constants_1 = require("./constants");
function getPackageVersion() {
    try {
        const packageJsonPath = (0, path_1.join)(__dirname, '..', 'package.json');
        const packageJson = JSON.parse((0, fs_1.readFileSync)(packageJsonPath, 'utf8'));
        return packageJson.version || '1.0.0';
    }
    catch {
        return '1.0.0';
    }
}
function createProgram() {
    const program = new commander_1.Command();
    program
        .name('convert-apps-to-homebrew')
        .description('Convert macOS applications to Homebrew installations with interactive selection')
        .version(getPackageVersion(), '-v, --version', 'display version number')
        .helpOption('-h, --help', 'display help for command');
    program
        .option('-i, --ignore <apps...>', 'ignore specific applications (can be used multiple times)', [])
        .option('-d, --dry-run', 'show what would be done without making any changes', false)
        .option('--verbose', 'enable verbose output for debugging', false)
        .option('--applications-dir <path>', 'specify custom Applications directory path', '/Applications');
    program.addHelpText('after', `
Examples:
  $ npx convert-apps-to-homebrew
  $ npx convert-apps-to-homebrew --dry-run
  $ npx convert-apps-to-homebrew --ignore "Adobe Photoshop" "Microsoft Word"
  $ npx convert-apps-to-homebrew --verbose --dry-run
  $ npx convert-apps-to-homebrew --applications-dir "/Applications"

Notes:
  • The tool will scan your Applications directory for .app bundles
  • It checks Homebrew for available casks and formulas
  • You can interactively select which apps to install via Homebrew
  • Original .app files are deleted only for cask installations (requires sudo)
  • Use --dry-run to preview changes without making them
  • Use --ignore to skip specific applications by name
`);
    return program;
}
function parseArguments(argv = process.argv) {
    const program = createProgram();
    try {
        program.parse(argv);
        const options = program.opts();
        if (options['applicationsDir'] && typeof options['applicationsDir'] !== 'string') {
            throw new Error('Applications directory must be a valid path');
        }
        const ignore = Array.isArray(options['ignore']) ? options['ignore'] :
            typeof options['ignore'] === 'string' ? [options['ignore']] : [];
        for (const app of ignore) {
            if (typeof app !== 'string' || app.trim().length === 0) {
                throw new Error(`Invalid app name in ignore list: "${app}"`);
            }
        }
        const parsedOptions = {
            ignore: ignore.map((app) => app.trim()),
            dryRun: Boolean(options['dryRun']),
            verbose: Boolean(options['verbose']),
            applicationsDir: options['applicationsDir'] || '/Applications'
        };
        return parsedOptions;
    }
    catch (error) {
        const logger = (0, utils_1.createLogger)(false);
        if (error.code === 'commander.helpDisplayed') {
            process.exit(0);
        }
        if (error.code === 'commander.version') {
            process.exit(0);
        }
        logger.error(`Command line parsing error: ${error.message}`);
        logger.info('Use --help for usage information');
        process.exit(1);
    }
}
function displayWelcome(options) {
    const logger = (0, utils_1.createLogger)(options.verbose);
    console.log((0, utils_1.colorize)('\n🍺 Convert Apps to Homebrew', 'BRIGHT'));
    console.log((0, utils_1.colorize)('═'.repeat(50), 'DIM'));
    if (options.dryRun) {
        console.log((0, utils_1.colorize)(`\n${constants_1.MESSAGES.DRY_RUN_MODE}`, 'YELLOW'));
    }
    logger.info(`Scanning directory: ${options.applicationsDir}`);
    if (options.ignore && options.ignore.length > 0) {
        logger.info(`Ignoring apps: ${options.ignore.join(', ')}`);
    }
    if (options.verbose) {
        logger.verbose('Verbose mode enabled');
    }
    console.log();
}
function displayTroubleshooting() {
    console.log((0, utils_1.colorize)('\n🔧 Troubleshooting', 'BRIGHT'));
    console.log((0, utils_1.colorize)('═'.repeat(50), 'DIM'));
    console.log(`
${(0, utils_1.colorize)('Common Issues:', 'YELLOW')}

${(0, utils_1.colorize)('1. Homebrew not installed:', 'CYAN')}
   Install Homebrew first: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

${(0, utils_1.colorize)('2. Permission denied:', 'CYAN')}
   Make sure you have read access to the Applications directory
   Some apps may require administrator privileges to delete

${(0, utils_1.colorize)('3. App not found in Homebrew:', 'CYAN')}
   Not all applications are available as Homebrew casks or formulas
   You can search manually: brew search <app-name>

${(0, utils_1.colorize)('4. Network issues:', 'CYAN')}
   Homebrew requires internet access to check package availability
   Check your network connection and try again

${(0, utils_1.colorize)('For more help:', 'GREEN')}
   • Visit: https://brew.sh/
   • Report issues: https://github.com/yourusername/convert-apps-to-homebrew/issues
`);
}
function setupSignalHandlers() {
    const logger = (0, utils_1.createLogger)(false);
    process.on('SIGINT', () => {
        logger.warn('\n\nOperation cancelled by user (Ctrl+C)');
        process.exit(130);
    });
    process.on('SIGTERM', () => {
        logger.warn('\n\nOperation terminated');
        process.exit(143);
    });
    process.on('uncaughtException', (error) => {
        logger.error(`Uncaught exception: ${error.message}`);
        if (process.env['NODE_ENV'] === 'development') {
            console.error(error.stack);
        }
        process.exit(1);
    });
    process.on('unhandledRejection', (reason, promise) => {
        logger.error(`Unhandled rejection at: ${promise}, reason: ${reason}`);
        process.exit(1);
    });
}
function validateEnvironment() {
    const logger = (0, utils_1.createLogger)(false);
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0] || '0', 10);
    if (majorVersion < 22) {
        logger.error(`Node.js version ${nodeVersion} is not supported. Please use Node.js 22 or later.`);
        process.exit(1);
    }
    if (process.platform !== 'darwin') {
        logger.error('This tool is designed for macOS only.');
        process.exit(1);
    }
    logger.verbose(`Runtime environment validated: Node.js ${nodeVersion} on ${process.platform}`);
}
//# sourceMappingURL=cli.js.map