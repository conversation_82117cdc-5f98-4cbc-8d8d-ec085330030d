{"version": 3, "file": "error-handler.js", "sourceRoot": "", "sources": ["../src/error-handler.ts"], "names": [], "mappings": ";;;AA8OA,wDAGC;AAKD,0CAKC;AAKD,4DAaC;AAKD,wDAIC;AAlRD,mCAAgF;AAChF,2CAAmD;AACnD,mCAAiD;AAKjD,MAAa,YAAY;IACf,MAAM,CAAS;IACf,OAAO,CAAU;IAEzB,YAAY,UAAmB,KAAK;QAClC,IAAI,CAAC,MAAM,GAAG,IAAA,oBAAY,EAAC,OAAO,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAKD,WAAW,CAAC,KAAY,EAAE,OAAgB;QACxC,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAElD,IAAI,KAAK,YAAY,wBAAgB,EAAE,CAAC;YACtC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,KAAuB,EAAE,OAAe;QACrE,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,iBAAS,CAAC,sBAAsB;gBACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,oBAAQ,CAAC,sBAAsB,GAAG,OAAO,EAAE,CAAC,CAAC;gBAClE,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACpC,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,sBAAsB,CAAC,CAAC;YAElD,KAAK,iBAAS,CAAC,iBAAiB;gBAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,oBAAQ,CAAC,iBAAiB,GAAG,OAAO,EAAE,CAAC,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,iBAAiB,CAAC,CAAC;YAE7C,KAAK,iBAAS,CAAC,aAAa;gBAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,OAAO,0CAA0C,CAAC,CAAC;gBAC9F,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC;YAEzC,KAAK,iBAAS,CAAC,cAAc;gBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC1E,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC;YAEzC,KAAK,iBAAS,CAAC,cAAc;gBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAChE,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC;YAEzC,KAAK,iBAAS,CAAC,aAAa;gBAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC/D,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC;YAEzC;gBACE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACnE,IAAI,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtE,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,KAAY,EAAE,OAAe;QACtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAGlE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;QACvG,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QAC9F,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,sBAAU,CAAC,aAAa,CAAC,CAAC;IACzC,CAAC;IAEO,4BAA4B;QAClC,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,sHAAsH,CAAC,CAAC;QACpI,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAC1D,CAAC;IAEO,kBAAkB;QACxB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,8EAA8E,CAAC,CAAC;QAC5F,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;QAC3E,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAClE,CAAC;IAEO,eAAe;QACrB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAEO,sBAAsB;QAC5B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAChE,CAAC;IAEO,oBAAoB;QAC1B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;IAC7E,CAAC;IAEO,uBAAuB;QAC7B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;CACF;AApID,oCAoIC;AAKD,MAAa,eAAe;IAClB,MAAM,CAAS;IACf,SAAS,CAAS;IAClB,UAAU,CAAS;IAE3B,YAAY,UAAmB,KAAK;QAClC,IAAI,CAAC,MAAM,GAAG,IAAA,oBAAY,EAAC,OAAO,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;IACnC,CAAC;IAKD,cAAc,CAAC,SAAiB,EAAE,KAAc;QAC9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,SAAS,KAAK,KAAK,YAAY,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,SAAS,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAKD,cAAc,CAAC,OAAe,EAAE,OAAgB,EAAE,KAAc;QAC9D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,MAAM,eAAe,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAG9C,IAAI,eAAe,GAAG,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAEtB,IAAI,WAAW,GAAG,OAAO,CAAC;QAE1B,IAAI,OAAO,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC3D,WAAW,GAAG,GAAG,OAAO,IAAI,WAAW,IAAI,OAAO,IAAI,KAAK,KAAK,UAAU,IAAI,CAAC;QACjF,CAAC;QAED,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;YAClD,WAAW,IAAI,KAAK,cAAc,IAAI,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC;IAKD,iBAAiB,CAAC,SAAiB,EAAE,UAAmB,IAAI;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5C,MAAM,cAAc,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEnD,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,iBAAiB,cAAc,GAAG,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,SAAS,6BAA6B,cAAc,GAAG,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,OAAe,EAAE,KAAa,EAAE,QAAgB,EAAE;QAC1E,MAAM,UAAU,GAAG,OAAO,GAAG,KAAK,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC;QAE7B,OAAO,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;IACvD,CAAC;CACF;AAhFD,0CAgFC;AAKD,IAAI,kBAAkB,GAAwB,IAAI,CAAC;AAKnD,SAAgB,sBAAsB,CAAC,UAAmB,KAAK;IAC7D,kBAAkB,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAKD,SAAgB,eAAe;IAC7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,kBAAkB,GAAG,IAAI,YAAY,EAAE,CAAC;IAC1C,CAAC;IACD,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAKD,SAAgB,wBAAwB,CAAC,UAAmB,KAAK;IAC/D,MAAM,YAAY,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAErD,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAY,EAAE,EAAE;QAC/C,OAAO,CAAC,KAAK,CAAC,IAAA,gBAAQ,EAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC,CAAC;QAC3D,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAW,EAAE,EAAE;QAC/C,OAAO,CAAC,KAAK,CAAC,IAAA,gBAAQ,EAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC,CAAC;QACpE,MAAM,KAAK,GAAG,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3E,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;AACL,CAAC;AAKD,SAAgB,sBAAsB,CAAC,OAAwB;IAC7D,OAAO,CAAC,OAAe,EAAE,OAAe,EAAE,KAAa,EAAE,EAAE;QACzD,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC"}