"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkHomebrewInstalled = checkHomebrewInstalled;
exports.getInstalledCasks = getInstalledCasks;
exports.getInstalledFormulas = getInstalledFormulas;
exports.isCaskAvailable = isCaskAvailable;
exports.isFormulaAvailable = isFormulaAvailable;
exports.scanApplicationsDirectory = scanApplicationsDirectory;
exports.determinePackageInfo = determinePackageInfo;
exports.checkAlreadyInstalled = checkAlreadyInstalled;
exports.discoverApps = discoverApps;
const fs_1 = require("fs");
const path_1 = require("path");
const child_process_1 = require("child_process");
const util_1 = require("util");
const types_1 = require("./types");
const constants_1 = require("./constants");
const utils_1 = require("./utils");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
async function executeCommand(command, timeout = constants_1.DEFAULT_CONFIG.BREW_COMMAND_TIMEOUT) {
    try {
        const { stdout, stderr } = await execAsync(command, { timeout });
        return {
            exitCode: 0,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            success: true
        };
    }
    catch (error) {
        return {
            exitCode: error.code || 1,
            stdout: error.stdout?.trim() || '',
            stderr: error.stderr?.trim() || error.message || '',
            success: false
        };
    }
}
async function checkHomebrewInstalled() {
    const result = await executeCommand(constants_1.BREW_COMMANDS.VERSION);
    return result.success;
}
async function getInstalledCasks() {
    const result = await executeCommand(constants_1.BREW_COMMANDS.LIST_CASKS);
    if (!result.success) {
        return [];
    }
    return (0, utils_1.parseCommandOutput)(result.stdout);
}
async function getInstalledFormulas() {
    const result = await executeCommand(constants_1.BREW_COMMANDS.LIST_FORMULAS);
    if (!result.success) {
        return [];
    }
    return (0, utils_1.parseCommandOutput)(result.stdout);
}
async function isCaskAvailable(packageName) {
    const result = await executeCommand(constants_1.BREW_COMMANDS.INFO_CASK(packageName));
    return result.success;
}
async function isFormulaAvailable(packageName) {
    const result = await executeCommand(constants_1.BREW_COMMANDS.INFO_FORMULA(packageName));
    return result.success;
}
async function scanApplicationsDirectory(applicationsDir = constants_1.DEFAULT_APPLICATIONS_DIR) {
    try {
        const entries = await fs_1.promises.readdir(applicationsDir, { withFileTypes: true });
        return entries
            .filter(entry => entry.isDirectory() && constants_1.FILE_PATTERNS.APP_PATTERN.test(entry.name))
            .map(entry => (0, path_1.join)(applicationsDir, entry.name));
    }
    catch (error) {
        if (error.code === 'ENOENT') {
            throw new types_1.ConvertAppsError(`Applications directory not found: ${applicationsDir}`, types_1.ErrorType.FILE_NOT_FOUND, error);
        }
        if (error.code === 'EACCES') {
            throw new types_1.ConvertAppsError(`Permission denied accessing: ${applicationsDir}`, types_1.ErrorType.PERMISSION_DENIED, error);
        }
        throw new types_1.ConvertAppsError(`Failed to scan applications directory: ${error.message}`, types_1.ErrorType.UNKNOWN_ERROR, error);
    }
}
async function determinePackageInfo(_appName, brewName) {
    const isCask = await isCaskAvailable(brewName);
    if (isCask) {
        return { brewType: 'cask', alreadyInstalled: false };
    }
    const isFormula = await isFormulaAvailable(brewName);
    if (isFormula) {
        return { brewType: 'formula', alreadyInstalled: false };
    }
    return { brewType: 'unavailable', alreadyInstalled: false };
}
async function checkAlreadyInstalled(apps) {
    const [installedCasks, installedFormulas] = await Promise.all([
        getInstalledCasks(),
        getInstalledFormulas()
    ]);
    const installedCaskSet = new Set(installedCasks);
    const installedFormulaSet = new Set(installedFormulas);
    return apps.map(app => ({
        ...app,
        alreadyInstalled: ((app.brewType === 'cask' && installedCaskSet.has(app.brewName)) ||
            (app.brewType === 'formula' && installedFormulaSet.has(app.brewName))),
        status: ((app.brewType === 'cask' && installedCaskSet.has(app.brewName)) ||
            (app.brewType === 'formula' && installedFormulaSet.has(app.brewName))) ? 'already-installed' : app.status
    }));
}
async function discoverApps(config) {
    const logger = (0, utils_1.createLogger)(config.verbose);
    logger.verbose('Starting app discovery...');
    const homebrewInstalled = await checkHomebrewInstalled();
    if (!homebrewInstalled) {
        throw new types_1.ConvertAppsError('Homebrew is not installed or not accessible', types_1.ErrorType.HOMEBREW_NOT_INSTALLED);
    }
    logger.verbose('Homebrew installation verified');
    logger.verbose(`Scanning ${config.applicationsDir}...`);
    const appPaths = await scanApplicationsDirectory(config.applicationsDir);
    if (appPaths.length === 0) {
        logger.warn('No applications found in the Applications directory');
        return [];
    }
    logger.verbose(`Found ${appPaths.length} applications`);
    const apps = [];
    const ignoredSet = new Set(config.ignoredApps.map(name => (0, utils_1.normalizeAppName)(name)));
    for (const appPath of appPaths) {
        const originalName = (0, utils_1.extractAppName)(appPath);
        const brewName = (0, utils_1.normalizeAppName)(originalName);
        if (ignoredSet.has(brewName)) {
            apps.push({
                originalName,
                brewName,
                appPath,
                brewType: 'unavailable',
                status: 'ignored',
                alreadyInstalled: false
            });
            continue;
        }
        logger.verbose(`Checking Homebrew availability for: ${originalName}`);
        try {
            const packageInfo = await determinePackageInfo(originalName, brewName);
            apps.push({
                originalName,
                brewName,
                appPath,
                brewType: packageInfo.brewType,
                status: packageInfo.brewType === 'unavailable' ? 'unavailable' : 'available',
                alreadyInstalled: packageInfo.alreadyInstalled
            });
        }
        catch (error) {
            logger.warn(`Failed to check Homebrew availability for ${originalName}: ${error}`);
            apps.push({
                originalName,
                brewName,
                appPath,
                brewType: 'unavailable',
                status: 'unavailable',
                alreadyInstalled: false
            });
        }
    }
    logger.verbose('Checking for already installed packages...');
    const appsWithInstallStatus = await checkAlreadyInstalled(apps);
    logger.info(`Discovery complete: ${appsWithInstallStatus.length} apps processed`);
    return appsWithInstallStatus;
}
//# sourceMappingURL=app-scanner.js.map