"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConvertAppsError = exports.ErrorType = void 0;
var ErrorType;
(function (ErrorType) {
    ErrorType["HOMEBREW_NOT_INSTALLED"] = "HOMEBREW_NOT_INSTALLED";
    ErrorType["PERMISSION_DENIED"] = "PERMISSION_DENIED";
    ErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorType["COMMAND_FAILED"] = "COMMAND_FAILED";
    ErrorType["FILE_NOT_FOUND"] = "FILE_NOT_FOUND";
    ErrorType["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
class ConvertAppsError extends Error {
    type;
    originalError;
    constructor(message, type, originalError) {
        super(message);
        this.type = type;
        this.originalError = originalError;
        this.name = 'ConvertAppsError';
    }
}
exports.ConvertAppsError = ConvertAppsError;
//# sourceMappingURL=types.js.map