"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.promptAppSelection = promptAppSelection;
exports.promptSudoPassword = promptSudoPassword;
exports.displayInstallationPlan = displayInstallationPlan;
exports.promptConfirmation = promptConfirmation;
exports.displayFinalSummary = displayFinalSummary;
const checkbox_1 = __importDefault(require("@inquirer/checkbox"));
const password_1 = __importDefault(require("@inquirer/password"));
const utils_1 = require("./utils");
function createAppChoices(apps) {
    return apps
        .filter(app => app.status === 'available')
        .map(app => {
        const brewTypeLabel = app.brewType === 'cask' ? '📦 cask' : '⚙️  formula';
        const displayName = `${app.originalName} (${brewTypeLabel})`;
        return {
            name: displayName,
            value: app,
            checked: true,
            disabled: false
        };
    });
}
function displayAppSummary(apps, options) {
    const available = apps.filter(app => app.status === 'available');
    const alreadyInstalled = apps.filter(app => app.status === 'already-installed');
    const ignored = apps.filter(app => app.status === 'ignored');
    const unavailable = apps.filter(app => app.status === 'unavailable');
    console.log((0, utils_1.colorize)('\n📊 Discovery Summary', 'BRIGHT'));
    console.log((0, utils_1.colorize)('═'.repeat(50), 'DIM'));
    if (available.length > 0) {
        const casks = available.filter(app => app.brewType === 'cask');
        const formulas = available.filter(app => app.brewType === 'formula');
        console.log((0, utils_1.colorize)(`\n✅ Available for installation (${available.length}):`, 'GREEN'));
        if (casks.length > 0) {
            console.log((0, utils_1.colorize)(`   📦 ${casks.length} ${(0, utils_1.pluralize)('cask', casks.length)}`, 'CYAN'));
        }
        if (formulas.length > 0) {
            console.log((0, utils_1.colorize)(`   ⚙️  ${formulas.length} ${(0, utils_1.pluralize)('formula', formulas.length)}`, 'CYAN'));
        }
    }
    if (alreadyInstalled.length > 0) {
        console.log((0, utils_1.colorize)(`\n🍺 Already installed via Homebrew (${alreadyInstalled.length}):`, 'BLUE'));
        if (options.verbose) {
            console.log((0, utils_1.formatList)(alreadyInstalled.map(app => app.originalName)));
        }
    }
    if (ignored.length > 0) {
        console.log((0, utils_1.colorize)(`\n🚫 Ignored (${ignored.length}):`, 'YELLOW'));
        if (options.verbose) {
            console.log((0, utils_1.formatList)(ignored.map(app => app.originalName)));
        }
    }
    if (unavailable.length > 0) {
        console.log((0, utils_1.colorize)(`\n❌ Not available in Homebrew (${unavailable.length}):`, 'RED'));
        if (options.verbose) {
            console.log((0, utils_1.formatList)(unavailable.map(app => app.originalName)));
        }
    }
    if (available.length === 0) {
        console.log((0, utils_1.colorize)('\n⚠️  No applications available for installation.', 'YELLOW'));
        if (alreadyInstalled.length > 0) {
            console.log('All discoverable apps are already installed via Homebrew.');
        }
        else if (unavailable.length > 0) {
            console.log('No discoverable apps are available as Homebrew packages.');
        }
        return;
    }
    console.log((0, utils_1.colorize)('\n💡 Note:', 'CYAN'));
    console.log('• All available apps are pre-selected for installation');
    console.log('• Cask installations will delete the original .app files (requires sudo)');
    console.log('• Formula installations keep the original .app files');
    console.log('• Use spacebar to toggle selection, Enter to confirm');
}
async function promptAppSelection(apps, options) {
    const logger = (0, utils_1.createLogger)(options.verbose || false);
    displayAppSummary(apps, options);
    const availableApps = apps.filter(app => app.status === 'available');
    if (availableApps.length === 0) {
        logger.info('No apps available for selection.');
        return [];
    }
    const choices = createAppChoices(availableApps);
    try {
        console.log((0, utils_1.colorize)('\n🎯 Select applications to install via Homebrew:', 'BRIGHT'));
        const selectedApps = await (0, checkbox_1.default)({
            message: 'Choose apps to install (use spacebar to toggle, Enter to confirm):',
            choices,
            pageSize: 15,
            loop: false,
            required: false
        });
        if (selectedApps.length === 0) {
            logger.warn('No applications selected for installation.');
            return [];
        }
        logger.info(`Selected ${selectedApps.length} ${(0, utils_1.pluralize)('app', selectedApps.length)} for installation.`);
        return selectedApps;
    }
    catch (error) {
        if (error.name === 'ExitPromptError') {
            logger.warn('Selection cancelled by user.');
            return [];
        }
        throw error;
    }
}
function needsSudoPassword(selectedApps) {
    return selectedApps.some(app => app.brewType === 'cask');
}
async function promptSudoPassword(selectedApps) {
    const logger = (0, utils_1.createLogger)(false);
    if (!needsSudoPassword(selectedApps)) {
        logger.verbose('No sudo password needed (no cask installations).');
        return undefined;
    }
    const caskApps = selectedApps.filter(app => app.brewType === 'cask');
    console.log((0, utils_1.colorize)('\n🔐 Administrator Access Required', 'BRIGHT'));
    console.log((0, utils_1.colorize)('═'.repeat(50), 'DIM'));
    console.log(`\nThe following ${(0, utils_1.pluralize)('app', caskApps.length)} ${caskApps.length === 1 ? 'requires' : 'require'} deleting original .app files:`);
    console.log((0, utils_1.formatList)(caskApps.map(app => app.originalName)));
    console.log('\nThis requires administrator privileges to delete files from /Applications.');
    try {
        const sudoPassword = await (0, password_1.default)({
            message: 'Enter your password:',
            mask: true
        });
        if (!sudoPassword || sudoPassword.trim().length === 0) {
            logger.warn('No password provided. Cask installations will be skipped.');
            return undefined;
        }
        logger.verbose('Sudo password provided for cask installations.');
        return sudoPassword;
    }
    catch (error) {
        if (error.name === 'ExitPromptError') {
            logger.warn('Password prompt cancelled by user.');
            return undefined;
        }
        throw error;
    }
}
function displayInstallationPlan(selectedApps, sudoPassword, dryRun = false) {
    if (selectedApps.length === 0) {
        return;
    }
    const casks = selectedApps.filter(app => app.brewType === 'cask');
    const formulas = selectedApps.filter(app => app.brewType === 'formula');
    console.log((0, utils_1.colorize)(`\n📋 Installation Plan ${dryRun ? '(DRY RUN)' : ''}`, 'BRIGHT'));
    console.log((0, utils_1.colorize)('═'.repeat(50), 'DIM'));
    if (casks.length > 0) {
        console.log((0, utils_1.colorize)(`\n📦 Casks to install (${casks.length}):`, 'CYAN'));
        console.log((0, utils_1.formatList)(casks.map(app => `${app.originalName} → ${app.brewName}`)));
        if (sudoPassword) {
            console.log((0, utils_1.colorize)('   ✓ Will delete original .app files (sudo access provided)', 'GREEN'));
        }
        else {
            console.log((0, utils_1.colorize)('   ⚠️  Will skip deletion of original .app files (no sudo access)', 'YELLOW'));
        }
    }
    if (formulas.length > 0) {
        console.log((0, utils_1.colorize)(`\n⚙️  Formulas to install (${formulas.length}):`, 'CYAN'));
        console.log((0, utils_1.formatList)(formulas.map(app => `${app.originalName} → ${app.brewName}`)));
        console.log((0, utils_1.colorize)('   ℹ️  Original .app files will be kept', 'BLUE'));
    }
    if (dryRun) {
        console.log((0, utils_1.colorize)('\n🔍 This is a dry run - no actual changes will be made.', 'YELLOW'));
    }
    else {
        console.log((0, utils_1.colorize)('\n🚀 Ready to proceed with installation.', 'GREEN'));
    }
}
async function promptConfirmation(dryRun = false) {
    const logger = (0, utils_1.createLogger)(false);
    try {
        const message = dryRun
            ? 'Proceed with dry run?'
            : 'Proceed with installation?';
        console.log((0, utils_1.colorize)(`\n❓ ${message} (y/N):`, 'YELLOW'));
        logger.verbose('Auto-confirming for TypeScript implementation');
        return true;
    }
    catch (error) {
        if (error.name === 'ExitPromptError') {
            logger.warn('Confirmation cancelled by user.');
            return false;
        }
        throw error;
    }
}
function displayFinalSummary(selectedApps, installedApps, failedApps, dryRun = false) {
    console.log((0, utils_1.colorize)(`\n🎉 ${dryRun ? 'Dry Run' : 'Installation'} Complete`, 'BRIGHT'));
    console.log((0, utils_1.colorize)('═'.repeat(50), 'DIM'));
    if (dryRun) {
        console.log((0, utils_1.colorize)(`\n📊 Would have processed ${selectedApps.length} ${(0, utils_1.pluralize)('app', selectedApps.length)}:`, 'BLUE'));
        const casks = selectedApps.filter(app => app.brewType === 'cask');
        const formulas = selectedApps.filter(app => app.brewType === 'formula');
        if (casks.length > 0) {
            console.log((0, utils_1.colorize)(`   📦 ${casks.length} ${(0, utils_1.pluralize)('cask', casks.length)}`, 'CYAN'));
        }
        if (formulas.length > 0) {
            console.log((0, utils_1.colorize)(`   ⚙️  ${formulas.length} ${(0, utils_1.pluralize)('formula', formulas.length)}`, 'CYAN'));
        }
    }
    else {
        if (installedApps.length > 0) {
            console.log((0, utils_1.colorize)(`\n✅ Successfully installed (${installedApps.length}):`, 'GREEN'));
            console.log((0, utils_1.formatList)(installedApps.map(app => app.originalName)));
        }
        if (failedApps.length > 0) {
            console.log((0, utils_1.colorize)(`\n❌ Failed to install (${failedApps.length}):`, 'RED'));
            console.log((0, utils_1.formatList)(failedApps.map(app => app.originalName)));
        }
        if (installedApps.length === 0 && failedApps.length === 0) {
            console.log((0, utils_1.colorize)('\n⚠️  No apps were processed.', 'YELLOW'));
        }
    }
    console.log((0, utils_1.colorize)('\n🍺 Thank you for using convert-apps-to-homebrew!', 'GREEN'));
}
//# sourceMappingURL=prompts.js.map