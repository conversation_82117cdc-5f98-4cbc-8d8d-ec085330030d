export declare const DEFAULT_APPLICATIONS_DIR = "/Applications";
export declare const BREW_COMMANDS: {
    readonly LIST_CASKS: "brew ls -1 --cask";
    readonly LIST_FORMULAS: "brew leaves";
    readonly INFO_CASK: (name: string) => string;
    readonly INFO_FORMULA: (name: string) => string;
    readonly INSTALL_CASK: (names: string[]) => string;
    readonly INSTALL_FORMULA: (names: string[]) => string;
    readonly VERSION: "brew --version";
};
export declare const FILE_PATTERNS: {
    readonly APP_EXTENSION: ".app";
    readonly APP_PATTERN: RegExp;
};
export declare const EXIT_CODES: {
    readonly SUCCESS: 0;
    readonly GENERAL_ERROR: 1;
    readonly HOMEBREW_NOT_INSTALLED: 2;
    readonly PERMISSION_DENIED: 3;
    readonly INVALID_INPUT: 4;
    readonly NETWORK_ERROR: 5;
};
export declare const DEFAULT_CONFIG: {
    readonly MAX_CONCURRENT_OPERATIONS: 5;
    readonly BREW_COMMAND_TIMEOUT: 30000;
    readonly VERBOSE: false;
    readonly DRY_RUN: false;
};
export declare const COLORS: {
    readonly RESET: "\u001B[0m";
    readonly BRIGHT: "\u001B[1m";
    readonly DIM: "\u001B[2m";
    readonly RED: "\u001B[31m";
    readonly GREEN: "\u001B[32m";
    readonly YELLOW: "\u001B[33m";
    readonly BLUE: "\u001B[34m";
    readonly MAGENTA: "\u001B[35m";
    readonly CYAN: "\u001B[36m";
    readonly WHITE: "\u001B[37m";
};
export declare const MESSAGES: {
    readonly HOMEBREW_NOT_INSTALLED: "Homebrew is not installed. Please install it before continuing.";
    readonly PERMISSION_DENIED: "Permission denied. You may need to run with appropriate permissions.";
    readonly NO_APPS_FOUND: "No applications found in the Applications directory.";
    readonly NO_APPS_SELECTED: "No applications selected for installation.";
    readonly DRY_RUN_MODE: "Running in dry-run mode. No actual changes will be made.";
    readonly OPERATION_CANCELLED: "Operation cancelled by user.";
    readonly SCANNING_APPS: "Scanning applications...";
    readonly CHECKING_HOMEBREW: "Checking Homebrew availability...";
    readonly INSTALLING_PACKAGES: "Installing packages...";
    readonly DELETING_APPS: "Deleting original applications...";
    readonly OPERATION_COMPLETE: "Operation completed successfully.";
};
export declare const REGEX_PATTERNS: {
    readonly BREW_PACKAGE_NAME: RegExp;
    readonly APP_NAME: RegExp;
    readonly VERSION: RegExp;
};
//# sourceMappingURL=constants.d.ts.map