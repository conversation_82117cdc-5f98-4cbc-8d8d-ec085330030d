"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeAppName = normalizeAppName;
exports.extractAppName = extractAppName;
exports.isValidBrewPackageName = isValidBrewPackageName;
exports.isValidAppName = isValidAppName;
exports.colorize = colorize;
exports.formatList = formatList;
exports.createLogger = createLogger;
exports.sleep = sleep;
exports.escapeShellArg = escapeShellArg;
exports.parseCommandOutput = parseCommandOutput;
exports.isEmpty = isEmpty;
exports.capitalize = capitalize;
exports.pluralize = pluralize;
exports.formatDuration = formatDuration;
exports.createProgressBar = createProgressBar;
exports.truncate = truncate;
exports.groupBy = groupBy;
exports.uniqueBy = uniqueBy;
const constants_1 = require("./constants");
function normalizeAppName(appName) {
    return appName
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9\-_.]/g, '')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '');
}
function extractAppName(appPath) {
    const basename = appPath.split('/').pop() || '';
    return basename.replace(/\.app$/i, '');
}
function isValidBrewPackageName(name) {
    return constants_1.REGEX_PATTERNS.BREW_PACKAGE_NAME.test(name);
}
function isValidAppName(name) {
    return constants_1.REGEX_PATTERNS.APP_NAME.test(name) && name.trim().length > 0;
}
function colorize(text, color) {
    return `${constants_1.COLORS[color]}${text}${constants_1.COLORS.RESET}`;
}
function formatList(items, indent = '  ') {
    return items.map(item => `${indent}• ${item}`).join('\n');
}
function createLogger(verbose = false) {
    return {
        info: (message) => {
            console.log(colorize(`ℹ ${message}`, 'BLUE'));
        },
        warn: (message) => {
            console.warn(colorize(`⚠ ${message}`, 'YELLOW'));
        },
        error: (message) => {
            console.error(colorize(`✗ ${message}`, 'RED'));
        },
        debug: (message) => {
            if (verbose) {
                console.log(colorize(`🐛 ${message}`, 'MAGENTA'));
            }
        },
        verbose: (message) => {
            if (verbose) {
                console.log(colorize(`📝 ${message}`, 'DIM'));
            }
        }
    };
}
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
function escapeShellArg(arg) {
    return `"${arg.replace(/"/g, '\\"')}"`;
}
function parseCommandOutput(output) {
    return output
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);
}
function isEmpty(str) {
    return !str || str.trim().length === 0;
}
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
function pluralize(word, count, suffix = 's') {
    return count === 1 ? word : word + suffix;
}
function formatDuration(ms) {
    if (ms < 1000) {
        return `${ms}ms`;
    }
    const seconds = Math.floor(ms / 1000);
    if (seconds < 60) {
        return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
}
function createProgressBar(current, total, width = 20) {
    const percentage = Math.min(current / total, 1);
    const filled = Math.floor(percentage * width);
    const empty = width - filled;
    const bar = '█'.repeat(filled) + '░'.repeat(empty);
    const percent = Math.floor(percentage * 100);
    return `[${bar}] ${percent}% (${current}/${total})`;
}
function truncate(str, maxLength) {
    if (str.length <= maxLength) {
        return str;
    }
    return str.slice(0, maxLength - 3) + '...';
}
function groupBy(items, keyFn) {
    return items.reduce((groups, item) => {
        const key = keyFn(item);
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(item);
        return groups;
    }, {});
}
function uniqueBy(items, keyFn) {
    const seen = new Set();
    return items.filter(item => {
        const key = keyFn(item);
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}
//# sourceMappingURL=utils.js.map