{"version": 3, "file": "prompts.js", "sourceRoot": "", "sources": ["../src/prompts.ts"], "names": [], "mappings": ";;;;;AA+FA,gDA6CC;AAYD,gDAqCC;AAKD,0DAqCC;AAKD,gDAwBC;AAKD,kDAqCC;AA1SD,kEAA0C;AAC1C,kEAA0C;AAE1C,mCAAwE;AAKxE,SAAS,gBAAgB,CAAC,IAAe;IACvC,OAAO,IAAI;SACR,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC;SACzC,GAAG,CAAC,GAAG,CAAC,EAAE;QACT,MAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC;QAC1E,MAAM,WAAW,GAAG,GAAG,GAAG,CAAC,YAAY,KAAK,aAAa,GAAG,CAAC;QAE7D,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC,CAAC,CAAC;AACP,CAAC;AAKD,SAAS,iBAAiB,CAAC,IAAe,EAAE,OAAuB;IAEjE,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;IACjE,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,mBAAmB,CAAC,CAAC;IAChF,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;IAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;IAErE,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAE7C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;QAErE,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,mCAAmC,SAAS,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QACxF,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,SAAS,KAAK,CAAC,MAAM,IAAI,IAAA,iBAAS,EAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,UAAU,QAAQ,CAAC,MAAM,IAAI,IAAA,iBAAS,EAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACtG,CAAC;IACH,CAAC;IAED,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,wCAAwC,gBAAgB,CAAC,MAAM,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QACnG,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,iBAAiB,OAAO,CAAC,MAAM,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrE,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,kCAAkC,WAAW,CAAC,MAAM,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACvF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,mDAAmD,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QAC3E,CAAC;aAAM,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO;IACT,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,0EAA0E,CAAC,CAAC;IACxF,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IACpE,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;AACtE,CAAC;AAKM,KAAK,UAAU,kBAAkB,CACtC,IAAe,EACf,OAAuB;IAEvB,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC;IAGtD,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEjC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;IAErE,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IAGD,MAAM,OAAO,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAEhD,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,mDAAmD,EAAE,QAAQ,CAAC,CAAC,CAAC;QAErF,MAAM,YAAY,GAAG,MAAM,IAAA,kBAAQ,EAAC;YAClC,OAAO,EAAE,oEAAoE;YAC7E,OAAO;YACP,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,KAAK;YACX,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QAEH,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,YAAY,YAAY,CAAC,MAAM,IAAI,IAAA,iBAAS,EAAC,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAC1G,OAAO,YAAY,CAAC;IAEtB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC5C,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,SAAS,iBAAiB,CAAC,YAAuB;IAChD,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;AAC3D,CAAC;AAKM,KAAK,UAAU,kBAAkB,CAAC,YAAuB;IAC9D,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,KAAK,CAAC,CAAC;IAEnC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACnE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;IAErE,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAA,iBAAS,EAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,gCAAgC,CAAC,CAAC;IACpJ,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/D,OAAO,CAAC,GAAG,CAAC,8EAA8E,CAAC,CAAC;IAE5F,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,IAAA,kBAAQ,EAAC;YAClC,OAAO,EAAE,sBAAsB;YAC/B,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;YACzE,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;QACjE,OAAO,YAAY,CAAC;IAEtB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,SAAgB,uBAAuB,CACrC,YAAuB,EACvB,YAAgC,EAChC,SAAkB,KAAK;IAEvB,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO;IACT,CAAC;IAED,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;IAClE,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;IAExE,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,0BAA0B,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IACvF,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAE7C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,0BAA0B,KAAK,CAAC,MAAM,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,YAAY,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QAEnF,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,6DAA6D,EAAE,OAAO,CAAC,CAAC,CAAC;QAChG,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,mEAAmE,EAAE,QAAQ,CAAC,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,8BAA8B,QAAQ,CAAC,MAAM,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QACjF,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,YAAY,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QACtF,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,yCAAyC,EAAE,MAAM,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,0DAA0D,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC9F,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,0CAA0C,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;AACH,CAAC;AAKM,KAAK,UAAU,kBAAkB,CAAC,SAAkB,KAAK;IAC9D,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,KAAK,CAAC,CAAC;IAEnC,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM;YACpB,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAC,4BAA4B,CAAC;QAIjC,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,OAAO,OAAO,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;QAIzD,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IAEd,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,SAAgB,mBAAmB,CACjC,YAAuB,EACvB,aAAwB,EACxB,UAAqB,EACrB,SAAkB,KAAK;IAEvB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,QAAQ,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxF,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAE7C,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,6BAA6B,YAAY,CAAC,MAAM,IAAI,IAAA,iBAAS,EAAC,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAC5H,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;QAClE,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;QAExE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,SAAS,KAAK,CAAC,MAAM,IAAI,IAAA,iBAAS,EAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,UAAU,QAAQ,CAAC,MAAM,IAAI,IAAA,iBAAS,EAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;QACtG,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,+BAA+B,aAAa,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YACxF,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,0BAA0B,UAAU,CAAC,MAAM,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YAC9E,OAAO,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1D,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,oDAAoD,EAAE,OAAO,CAAC,CAAC,CAAC;AACvF,CAAC"}