import { AppInfo, ScannerConfig } from './types';
export declare function checkHomebrewInstalled(): Promise<boolean>;
export declare function getInstalledCasks(): Promise<string[]>;
export declare function getInstalledFormulas(): Promise<string[]>;
export declare function isCaskAvailable(packageName: string): Promise<boolean>;
export declare function isFormulaAvailable(packageName: string): Promise<boolean>;
export declare function scanApplicationsDirectory(applicationsDir?: string): Promise<string[]>;
export declare function determinePackageInfo(_appName: string, brewName: string): Promise<{
    brewType: 'cask' | 'formula' | 'unavailable';
    alreadyInstalled: boolean;
}>;
export declare function checkAlreadyInstalled(apps: AppInfo[]): Promise<AppInfo[]>;
export declare function discoverApps(config: ScannerConfig): Promise<AppInfo[]>;
//# sourceMappingURL=app-scanner.d.ts.map