import { ProgressCallback } from './types';
export declare class ErrorHandler {
    private logger;
    private verbose;
    constructor(verbose?: boolean);
    handleError(error: Error, context?: string): never;
    private handleConvertAppsError;
    private handleGenericError;
    private showHomebrewInstallationHelp;
    private showPermissionHelp;
    private showNetworkHelp;
    private showCommandFailureHelp;
    private showFileNotFoundHelp;
    private showInputValidationHelp;
}
export declare class ProgressTracker {
    private logger;
    private startTime;
    private lastUpdate;
    constructor(verbose?: boolean);
    startOperation(operation: string, total?: number): void;
    updateProgress(message: string, current?: number, total?: number): void;
    completeOperation(operation: string, success?: boolean): void;
    private createProgressBar;
}
export declare function initializeErrorHandler(verbose?: boolean): ErrorHandler;
export declare function getErrorHandler(): ErrorHandler;
export declare function setupGlobalErrorHandlers(verbose?: boolean): void;
export declare function createProgressCallback(tracker: ProgressTracker): ProgressCallback;
//# sourceMappingURL=error-handler.d.ts.map