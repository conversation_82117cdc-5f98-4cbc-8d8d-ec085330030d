{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../src/constants.ts"], "names": [], "mappings": ";;;AAOa,QAAA,wBAAwB,GAAG,eAAe,CAAC;AAK3C,QAAA,aAAa,GAAG;IAE3B,UAAU,EAAE,mBAAmB;IAE/B,aAAa,EAAE,aAAa;IAE5B,SAAS,EAAE,CAAC,IAAY,EAAU,EAAE,CAAC,qBAAqB,IAAI,GAAG;IAEjE,YAAY,EAAE,CAAC,IAAY,EAAU,EAAE,CAAC,cAAc,IAAI,GAAG;IAE7D,YAAY,EAAE,CAAC,KAAe,EAAU,EAAE,CAAC,uBAAuB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IAEtG,eAAe,EAAE,CAAC,KAAe,EAAU,EAAE,CAAC,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IAElG,OAAO,EAAE,gBAAgB;CACjB,CAAC;AAKE,QAAA,aAAa,GAAG;IAE3B,aAAa,EAAE,MAAM;IAErB,WAAW,EAAE,SAAS;CACd,CAAC;AAKE,QAAA,UAAU,GAAG;IACxB,OAAO,EAAE,CAAC;IACV,aAAa,EAAE,CAAC;IAChB,sBAAsB,EAAE,CAAC;IACzB,iBAAiB,EAAE,CAAC;IACpB,aAAa,EAAE,CAAC;IAChB,aAAa,EAAE,CAAC;CACR,CAAC;AAKE,QAAA,cAAc,GAAG;IAE5B,yBAAyB,EAAE,CAAC;IAE5B,oBAAoB,EAAE,KAAK;IAE3B,OAAO,EAAE,KAAK;IAEd,OAAO,EAAE,KAAK;CACN,CAAC;AAKE,QAAA,MAAM,GAAG;IACpB,KAAK,EAAE,SAAS;IAChB,MAAM,EAAE,SAAS;IACjB,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,UAAU;IACf,KAAK,EAAE,UAAU;IACjB,MAAM,EAAE,UAAU;IAClB,IAAI,EAAE,UAAU;IAChB,OAAO,EAAE,UAAU;IACnB,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,UAAU;CACT,CAAC;AAKE,QAAA,QAAQ,GAAG;IACtB,sBAAsB,EAAE,iEAAiE;IACzF,iBAAiB,EAAE,sEAAsE;IACzF,aAAa,EAAE,sDAAsD;IACrE,gBAAgB,EAAE,4CAA4C;IAC9D,YAAY,EAAE,0DAA0D;IACxE,mBAAmB,EAAE,8BAA8B;IACnD,aAAa,EAAE,0BAA0B;IACzC,iBAAiB,EAAE,mCAAmC;IACtD,mBAAmB,EAAE,wBAAwB;IAC7C,aAAa,EAAE,mCAAmC;IAClD,kBAAkB,EAAE,mCAAmC;CAC/C,CAAC;AAKE,QAAA,cAAc,GAAG;IAE5B,iBAAiB,EAAE,0BAA0B;IAE7C,QAAQ,EAAE,YAAY;IAEtB,OAAO,EAAE,gBAAgB;CACjB,CAAC"}