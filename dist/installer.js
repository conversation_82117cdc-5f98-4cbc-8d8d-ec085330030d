"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.installApps = installApps;
exports.validateInstallationPrerequisites = validateInstallationPrerequisites;
exports.getInstallationSummary = getInstallationSummary;
const child_process_1 = require("child_process");
const util_1 = require("util");
const fs_1 = require("fs");
const types_1 = require("./types");
const constants_1 = require("./constants");
const utils_1 = require("./utils");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
async function executeCommand(command, dryRun = false, timeout = constants_1.DEFAULT_CONFIG.BREW_COMMAND_TIMEOUT) {
    if (dryRun) {
        return {
            exitCode: 0,
            stdout: `[DRY RUN] Would execute: ${command}`,
            stderr: '',
            success: true
        };
    }
    try {
        const { stdout, stderr } = await execAsync(command, { timeout });
        return {
            exitCode: 0,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            success: true
        };
    }
    catch (error) {
        return {
            exitCode: error.code || 1,
            stdout: error.stdout?.trim() || '',
            stderr: error.stderr?.trim() || error.message || '',
            success: false
        };
    }
}
async function executeSudoCommand(command, password, dryRun = false, timeout = constants_1.DEFAULT_CONFIG.BREW_COMMAND_TIMEOUT) {
    if (dryRun) {
        return {
            exitCode: 0,
            stdout: `[DRY RUN] Would execute with sudo: ${command}`,
            stderr: '',
            success: true
        };
    }
    try {
        const sudoCommand = `echo ${(0, utils_1.escapeShellArg)(password)} | sudo -S ${command}`;
        const { stdout, stderr } = await execAsync(sudoCommand, { timeout });
        return {
            exitCode: 0,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            success: true
        };
    }
    catch (error) {
        return {
            exitCode: error.code || 1,
            stdout: error.stdout?.trim() || '',
            stderr: error.stderr?.trim() || error.message || '',
            success: false
        };
    }
}
async function installCasks(casks, config, logger) {
    if (casks.length === 0) {
        return [];
    }
    const caskNames = casks.map(app => app.brewName);
    const command = constants_1.BREW_COMMANDS.INSTALL_CASK(caskNames);
    logger.info(`${config.dryRun ? '[DRY RUN] ' : ''}Installing ${casks.length} cask(s): ${caskNames.join(', ')}`);
    logger.verbose(`Command: ${command}`);
    const result = await executeCommand(command, config.dryRun);
    if (result.success) {
        logger.info(`Successfully installed ${casks.length} cask(s)`);
        return casks.map(app => ({
            packageName: app.brewName,
            appName: app.originalName,
            success: true,
            dryRun: config.dryRun
        }));
    }
    else {
        logger.error(`Failed to install casks: ${result.stderr}`);
        return casks.map(app => ({
            packageName: app.brewName,
            appName: app.originalName,
            success: false,
            error: result.stderr,
            dryRun: config.dryRun
        }));
    }
}
async function installFormulas(formulas, config, logger) {
    if (formulas.length === 0) {
        return [];
    }
    const formulaNames = formulas.map(app => app.brewName);
    const command = constants_1.BREW_COMMANDS.INSTALL_FORMULA(formulaNames);
    logger.info(`${config.dryRun ? '[DRY RUN] ' : ''}Installing ${formulas.length} formula(s): ${formulaNames.join(', ')}`);
    logger.verbose(`Command: ${command}`);
    const result = await executeCommand(command, config.dryRun);
    if (result.success) {
        logger.info(`Successfully installed ${formulas.length} formula(s)`);
        return formulas.map(app => ({
            packageName: app.brewName,
            appName: app.originalName,
            success: true,
            dryRun: config.dryRun
        }));
    }
    else {
        logger.error(`Failed to install formulas: ${result.stderr}`);
        return formulas.map(app => ({
            packageName: app.brewName,
            appName: app.originalName,
            success: false,
            error: result.stderr,
            dryRun: config.dryRun
        }));
    }
}
async function deleteOriginalApps(installedCasks, caskApps, sudoPassword, config, logger) {
    const successfulCasks = installedCasks.filter(result => result.success);
    if (successfulCasks.length === 0) {
        logger.verbose('No successful cask installations to clean up');
        return;
    }
    logger.info(`${config.dryRun ? '[DRY RUN] ' : ''}Deleting ${successfulCasks.length} original .app file(s)`);
    for (const result of successfulCasks) {
        const app = caskApps.find(a => a.brewName === result.packageName);
        if (!app) {
            logger.warn(`Could not find app info for ${result.packageName}`);
            continue;
        }
        try {
            if (config.dryRun) {
                logger.verbose(`[DRY RUN] Would delete: ${app.appPath}`);
                continue;
            }
            try {
                await fs_1.promises.access(app.appPath);
            }
            catch {
                logger.verbose(`App file already removed: ${app.appPath}`);
                continue;
            }
            const deleteCommand = `rm -rf ${(0, utils_1.escapeShellArg)(app.appPath)}`;
            const deleteResult = await executeSudoCommand(deleteCommand, sudoPassword, config.dryRun);
            if (deleteResult.success) {
                logger.verbose(`Deleted: ${app.appPath}`);
            }
            else {
                logger.warn(`Failed to delete ${app.appPath}: ${deleteResult.stderr}`);
            }
        }
        catch (error) {
            logger.warn(`Error deleting ${app.appPath}: ${error.message}`);
        }
    }
}
async function installApps(selectedApps, config) {
    const logger = (0, utils_1.createLogger)(config.verbose);
    if (selectedApps.length === 0) {
        logger.info('No apps selected for installation');
        return {
            installed: [],
            failed: [],
            alreadyInstalled: [],
            ignored: [],
            unavailable: [],
            dryRun: config.dryRun
        };
    }
    const appsByType = (0, utils_1.groupBy)(selectedApps, app => app.brewType);
    const casks = appsByType['cask'] || [];
    const formulas = appsByType['formula'] || [];
    logger.info(`Starting installation: ${casks.length} cask(s), ${formulas.length} formula(s)`);
    const allResults = [];
    try {
        if (casks.length > 0) {
            const caskResults = await installCasks(casks, config, logger);
            allResults.push(...caskResults);
            if (config.sudoPassword && !config.dryRun) {
                await deleteOriginalApps(caskResults, casks, config.sudoPassword, config, logger);
            }
            else if (casks.length > 0 && !config.sudoPassword && !config.dryRun) {
                logger.warn('No sudo password provided - original .app files will not be deleted');
            }
        }
        if (formulas.length > 0) {
            const formulaResults = await installFormulas(formulas, config, logger);
            allResults.push(...formulaResults);
        }
        const installed = allResults.filter(result => result.success);
        const failed = allResults.filter(result => !result.success);
        logger.info(`Installation complete: ${installed.length} successful, ${failed.length} failed`);
        return {
            installed,
            failed,
            alreadyInstalled: [],
            ignored: [],
            unavailable: [],
            dryRun: config.dryRun
        };
    }
    catch (error) {
        logger.error(`Installation failed: ${error.message}`);
        throw new types_1.ConvertAppsError(`Installation process failed: ${error.message}`, types_1.ErrorType.COMMAND_FAILED, error);
    }
}
async function validateInstallationPrerequisites() {
    const brewCheck = await executeCommand(constants_1.BREW_COMMANDS.VERSION, false, 5000);
    if (!brewCheck.success) {
        throw new types_1.ConvertAppsError('Homebrew is not installed or not accessible', types_1.ErrorType.HOMEBREW_NOT_INSTALLED);
    }
}
function getInstallationSummary(result) {
    const lines = [];
    if (result.dryRun) {
        lines.push('🔍 DRY RUN SUMMARY');
        lines.push('═'.repeat(50));
    }
    else {
        lines.push('📊 INSTALLATION SUMMARY');
        lines.push('═'.repeat(50));
    }
    if (result.installed.length > 0) {
        lines.push(`✅ Successfully installed: ${result.installed.length}`);
        result.installed.forEach(app => {
            lines.push(`   • ${app.appName} (${app.packageName})`);
        });
    }
    if (result.failed.length > 0) {
        lines.push(`❌ Failed to install: ${result.failed.length}`);
        result.failed.forEach(app => {
            lines.push(`   • ${app.appName} (${app.packageName}): ${app.error || 'Unknown error'}`);
        });
    }
    if (result.installed.length === 0 && result.failed.length === 0) {
        lines.push('⚠️  No packages were processed');
    }
    return lines.join('\n');
}
//# sourceMappingURL=installer.js.map