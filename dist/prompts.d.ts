import { AppInfo, CommandOptions } from './types';
export declare function promptAppSelection(apps: AppInfo[], options: CommandOptions): Promise<AppInfo[]>;
export declare function promptSudoPassword(selectedApps: AppInfo[]): Promise<string | undefined>;
export declare function displayInstallationPlan(selectedApps: AppInfo[], sudoPassword: string | undefined, dryRun?: boolean): void;
export declare function promptConfirmation(dryRun?: boolean): Promise<boolean>;
export declare function displayFinalSummary(selectedApps: AppInfo[], installedApps: AppInfo[], failedApps: AppInfo[], dryRun?: boolean): void;
//# sourceMappingURL=prompts.d.ts.map