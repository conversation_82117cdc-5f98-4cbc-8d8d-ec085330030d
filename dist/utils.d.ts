import { COLORS } from './constants';
import { Logger } from './types';
export declare function normalizeAppName(appName: string): string;
export declare function extractAppName(appPath: string): string;
export declare function isValidBrewPackageName(name: string): boolean;
export declare function isValidAppName(name: string): boolean;
export declare function colorize(text: string, color: keyof typeof COLORS): string;
export declare function formatList(items: string[], indent?: string): string;
export declare function createLogger(verbose?: boolean): Logger;
export declare function sleep(ms: number): Promise<void>;
export declare function escapeShellArg(arg: string): string;
export declare function parseCommandOutput(output: string): string[];
export declare function isEmpty(str: string | undefined | null): boolean;
export declare function capitalize(str: string): string;
export declare function pluralize(word: string, count: number, suffix?: string): string;
export declare function formatDuration(ms: number): string;
export declare function createProgressBar(current: number, total: number, width?: number): string;
export declare function truncate(str: string, maxLength: number): string;
export declare function groupBy<T, K extends string | number | symbol>(items: T[], keyFn: (item: T) => K): Record<K, T[]>;
export declare function uniqueBy<T, K>(items: T[], keyFn: (item: T) => K): T[];
//# sourceMappingURL=utils.d.ts.map