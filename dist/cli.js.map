{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;AA2BA,sCAmDC;AAKD,wCAiDC;AAKD,wCAqBC;AAKD,wDAyBC;AAKD,kDAyBC;AAKD,kDAmBC;AA9OD,yCAAoC;AACpC,2BAAkC;AAClC,+BAA4B;AAE5B,mCAAiD;AACjD,2CAAuC;AAKvC,SAAS,iBAAiB;IACxB,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;QACtE,OAAO,WAAW,CAAC,OAAO,IAAI,OAAO,CAAC;IACxC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAKD,SAAgB,aAAa;IAC3B,MAAM,OAAO,GAAG,IAAI,mBAAO,EAAE,CAAC;IAE9B,OAAO;SACJ,IAAI,CAAC,0BAA0B,CAAC;SAChC,WAAW,CAAC,iFAAiF,CAAC;SAC9F,OAAO,CAAC,iBAAiB,EAAE,EAAE,eAAe,EAAE,wBAAwB,CAAC;SACvE,UAAU,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAGxD,OAAO;SACJ,MAAM,CACL,wBAAwB,EACxB,2DAA2D,EAC3D,EAAE,CACH;SACA,MAAM,CACL,eAAe,EACf,oDAAoD,EACpD,KAAK,CACN;SACA,MAAM,CACL,WAAW,EACX,qCAAqC,EACrC,KAAK,CACN;SACA,MAAM,CACL,2BAA2B,EAC3B,4CAA4C,EAC5C,eAAe,CAChB,CAAC;IAGJ,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;;;;CAe9B,CAAC,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAKD,SAAgB,cAAc,CAAC,OAAiB,OAAO,CAAC,IAAI;IAC1D,MAAM,OAAO,GAAG,aAAa,EAAE,CAAC;IAEhC,IAAI,CAAC;QACH,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAG/B,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO,OAAO,CAAC,iBAAiB,CAAC,KAAK,QAAQ,EAAE,CAAC;YACjF,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAGD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnE,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAGnE,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACzB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvD,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,GAAG,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAmB;YACpC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAC/C,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAClC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACpC,eAAe,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,eAAe;SAC/D,CAAC;QAEF,OAAO,aAAa,CAAC;IACvB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,KAAK,CAAC,CAAC;QAEnC,IAAI,KAAK,CAAC,IAAI,KAAK,yBAAyB,EAAE,CAAC;YAE7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAEvC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAGD,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAKD,SAAgB,cAAc,CAAC,OAAuB;IACpD,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE7C,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,CAAC;IACjE,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAE7C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,KAAK,oBAAQ,CAAC,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;IAE9D,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChD,MAAM,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IACzC,CAAC;IAED,OAAO,CAAC,GAAG,EAAE,CAAC;AAChB,CAAC;AAKD,SAAgB,sBAAsB;IACpC,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxD,OAAO,CAAC,GAAG,CAAC,IAAA,gBAAQ,EAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC;EACZ,IAAA,gBAAQ,EAAC,gBAAgB,EAAE,QAAQ,CAAC;;EAEpC,IAAA,gBAAQ,EAAC,4BAA4B,EAAE,MAAM,CAAC;;;EAG9C,IAAA,gBAAQ,EAAC,uBAAuB,EAAE,MAAM,CAAC;;;;EAIzC,IAAA,gBAAQ,EAAC,+BAA+B,EAAE,MAAM,CAAC;;;;EAIjD,IAAA,gBAAQ,EAAC,oBAAoB,EAAE,MAAM,CAAC;;;;EAItC,IAAA,gBAAQ,EAAC,gBAAgB,EAAE,OAAO,CAAC;;;CAGpC,CAAC,CAAC;AACH,CAAC;AAKD,SAAgB,mBAAmB;IACjC,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,KAAK,CAAC,CAAC;IAEnC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACxB,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACxD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;QACzB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;QACxC,MAAM,CAAC,KAAK,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACrD,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa,EAAE,CAAC;YAC9C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;QACnD,MAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,aAAa,MAAM,EAAE,CAAC,CAAC;QACtE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC;AAKD,SAAgB,mBAAmB;IACjC,MAAM,MAAM,GAAG,IAAA,oBAAY,EAAC,KAAK,CAAC,CAAC;IAGnC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;IACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;IAE7E,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;QACtB,MAAM,CAAC,KAAK,CAAC,mBAAmB,WAAW,oDAAoD,CAAC,CAAC;QACjG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAGD,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClC,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACtD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,0CAA0C,WAAW,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjG,CAAC"}