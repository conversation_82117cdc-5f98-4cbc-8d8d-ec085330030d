export type BrewPackageType = 'cask' | 'formula' | 'unavailable';
export type AppStatus = 'available' | 'already-installed' | 'unavailable' | 'ignored';
export interface AppInfo {
    originalName: string;
    brewName: string;
    appPath: string;
    brewType: BrewPackageType;
    status: AppStatus;
    alreadyInstalled: boolean;
}
export interface CommandOptions {
    ignore?: string[];
    dryRun?: boolean;
    verbose?: boolean;
    applicationsDir?: string;
}
export interface BrewCommandResult {
    exitCode: number;
    stdout: string;
    stderr: string;
    success: boolean;
}
export interface PackageInstallResult {
    packageName: string;
    appName: string;
    success: boolean;
    error?: string;
    dryRun: boolean;
}
export interface InstallationResult {
    installed: PackageInstallResult[];
    failed: PackageInstallResult[];
    alreadyInstalled: AppInfo[];
    ignored: AppInfo[];
    unavailable: AppInfo[];
    dryRun: boolean;
}
export interface ScannerConfig {
    applicationsDir: string;
    ignoredApps: string[];
    verbose: boolean;
}
export interface InstallerConfig {
    dryRun: boolean;
    verbose: boolean;
    sudoPassword?: string;
}
export interface AppChoice {
    name: string;
    value: AppInfo;
    checked: boolean;
    disabled?: boolean | string;
}
export declare enum ErrorType {
    HOMEBREW_NOT_INSTALLED = "HOMEBREW_NOT_INSTALLED",
    PERMISSION_DENIED = "PERMISSION_DENIED",
    NETWORK_ERROR = "NETWORK_ERROR",
    COMMAND_FAILED = "COMMAND_FAILED",
    FILE_NOT_FOUND = "FILE_NOT_FOUND",
    INVALID_INPUT = "INVALID_INPUT",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
export declare class ConvertAppsError extends Error {
    readonly type: ErrorType;
    readonly originalError?: Error | undefined;
    constructor(message: string, type: ErrorType, originalError?: Error | undefined);
}
export type ProgressCallback = (message: string, current: number, total: number) => void;
export interface Logger {
    info(message: string): void;
    warn(message: string): void;
    error(message: string): void;
    debug(message: string): void;
    verbose(message: string): void;
}
export interface OperationSummary {
    totalApps: number;
    availableApps: number;
    alreadyInstalled: number;
    ignored: number;
    unavailable: number;
    selected: number;
    installed: number;
    failed: number;
    dryRun: boolean;
}
//# sourceMappingURL=types.d.ts.map