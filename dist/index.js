#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const cli_1 = require("./cli");
const app_scanner_1 = require("./app-scanner");
const prompts_1 = require("./prompts");
const installer_1 = require("./installer");
const types_1 = require("./types");
const constants_1 = require("./constants");
const utils_1 = require("./utils");
function createScannerConfig(options) {
    return {
        applicationsDir: options.applicationsDir || '/Applications',
        ignoredApps: options.ignore || [],
        verbose: options.verbose || false
    };
}
function createInstallerConfig(options, sudoPassword) {
    const config = {
        dryRun: options.dryRun || false,
        verbose: options.verbose || false
    };
    if (sudoPassword !== undefined) {
        config.sudoPassword = sudoPassword;
    }
    return config;
}
function generateOperationSummary(allApps, selectedApps, installationResult, dryRun) {
    const available = allApps.filter(app => app.status === 'available');
    const alreadyInstalled = allApps.filter(app => app.status === 'already-installed');
    const ignored = allApps.filter(app => app.status === 'ignored');
    const unavailable = allApps.filter(app => app.status === 'unavailable');
    return {
        totalApps: allApps.length,
        availableApps: available.length,
        alreadyInstalled: alreadyInstalled.length,
        ignored: ignored.length,
        unavailable: unavailable.length,
        selected: selectedApps.length,
        installed: installationResult.installed.length,
        failed: installationResult.failed.length,
        dryRun
    };
}
function handleError(error, logger) {
    if (error instanceof types_1.ConvertAppsError) {
        switch (error.type) {
            case types_1.ErrorType.HOMEBREW_NOT_INSTALLED:
                logger.error(constants_1.MESSAGES.HOMEBREW_NOT_INSTALLED);
                logger.info('Install Homebrew: https://brew.sh/');
                process.exit(constants_1.EXIT_CODES.HOMEBREW_NOT_INSTALLED);
            case types_1.ErrorType.PERMISSION_DENIED:
                logger.error(constants_1.MESSAGES.PERMISSION_DENIED);
                logger.info('Try running with appropriate permissions or check file access.');
                process.exit(constants_1.EXIT_CODES.PERMISSION_DENIED);
            case types_1.ErrorType.NETWORK_ERROR:
                logger.error('Network error occurred. Please check your internet connection.');
                process.exit(constants_1.EXIT_CODES.NETWORK_ERROR);
            case types_1.ErrorType.INVALID_INPUT:
                logger.error(`Invalid input: ${error.message}`);
                process.exit(constants_1.EXIT_CODES.INVALID_INPUT);
            default:
                logger.error(`Application error: ${error.message}`);
                if (error.originalError && logger.verbose) {
                    logger.debug(`Original error: ${error.originalError.message}`);
                }
                process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
        }
    }
    else {
        logger.error(`Unexpected error: ${error.message}`);
        if (logger.verbose) {
            logger.debug(error.stack);
        }
        process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
    }
}
async function main() {
    try {
        (0, cli_1.setupSignalHandlers)();
        (0, cli_1.validateEnvironment)();
        const options = (0, cli_1.parseArguments)();
        const logger = (0, utils_1.createLogger)(options.verbose || false);
        (0, cli_1.displayWelcome)(options);
        logger.info('Validating prerequisites...');
        await (0, installer_1.validateInstallationPrerequisites)();
        logger.info(constants_1.MESSAGES.SCANNING_APPS);
        const scannerConfig = createScannerConfig(options);
        const discoveredApps = await (0, app_scanner_1.discoverApps)(scannerConfig);
        if (discoveredApps.length === 0) {
            logger.warn(constants_1.MESSAGES.NO_APPS_FOUND);
            process.exit(constants_1.EXIT_CODES.SUCCESS);
        }
        const selectedApps = await (0, prompts_1.promptAppSelection)(discoveredApps, options);
        if (selectedApps.length === 0) {
            logger.info(constants_1.MESSAGES.NO_APPS_SELECTED);
            logger.info('Run the command again to select different apps.');
            process.exit(constants_1.EXIT_CODES.SUCCESS);
        }
        const sudoPassword = await (0, prompts_1.promptSudoPassword)(selectedApps);
        (0, prompts_1.displayInstallationPlan)(selectedApps, sudoPassword, options.dryRun);
        const confirmed = await (0, prompts_1.promptConfirmation)(options.dryRun);
        if (!confirmed) {
            logger.info(constants_1.MESSAGES.OPERATION_CANCELLED);
            process.exit(constants_1.EXIT_CODES.SUCCESS);
        }
        logger.info(options.dryRun ? 'Starting dry run...' : constants_1.MESSAGES.INSTALLING_PACKAGES);
        const installerConfig = createInstallerConfig(options, sudoPassword);
        const installationResult = await (0, installer_1.installApps)(selectedApps, installerConfig);
        console.log('\n' + (0, installer_1.getInstallationSummary)(installationResult));
        const installedApps = selectedApps.filter(app => installationResult.installed.some(result => result.packageName === app.brewName));
        const failedApps = selectedApps.filter(app => installationResult.failed.some(result => result.packageName === app.brewName));
        (0, prompts_1.displayFinalSummary)(selectedApps, installedApps, failedApps, options.dryRun);
        const summary = generateOperationSummary(discoveredApps, selectedApps, installationResult, options.dryRun || false);
        logger.verbose(`Operation summary: ${JSON.stringify(summary, null, 2)}`);
        if (installationResult.failed.length > 0) {
            logger.warn(`${installationResult.failed.length} installations failed.`);
            process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
        }
        else {
            logger.info(constants_1.MESSAGES.OPERATION_COMPLETE);
            process.exit(constants_1.EXIT_CODES.SUCCESS);
        }
    }
    catch (error) {
        const logger = (0, utils_1.createLogger)(false);
        if (error.name === 'ExitPromptError') {
            logger.info('\nOperation cancelled by user.');
            process.exit(constants_1.EXIT_CODES.SUCCESS);
        }
        handleError(error, logger);
    }
}
if (require.main === module) {
    main().catch((error) => {
        const logger = (0, utils_1.createLogger)(false);
        logger.error(`Fatal error: ${error.message}`);
        if (error.message.includes('Homebrew') ||
            error.message.includes('permission') ||
            error.message.includes('ENOENT')) {
            (0, cli_1.displayTroubleshooting)();
        }
        process.exit(constants_1.EXIT_CODES.GENERAL_ERROR);
    });
}
//# sourceMappingURL=index.js.map