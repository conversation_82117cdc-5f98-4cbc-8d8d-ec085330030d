TN:
SF:src/app-scanner.ts
FN:35,executeCommand
FN:57,checkHomebrewInstalled
FN:65,getInstalledCasks
FN:76,getInstalledFormulas
FN:87,isCaskAvailable
FN:95,isFormulaAvailable
FN:103,scanApplicationsDirectory
FN:108,(anonymous_7)
FN:109,(anonymous_8)
FN:136,determinePackageInfo
FN:158,checkAlreadyInstalled
FN:167,(anonymous_11)
FN:183,discoverApps
FN:212,(anonymous_13)
FNF:14
FNH:7
FNDA:2,executeCommand
FNDA:0,checkHomebrewInstalled
FNDA:1,getInstalledCasks
FNDA:1,getInstalledFormulas
FNDA:0,isCaskAvailable
FNDA:0,isFormulaAvailable
FNDA:5,scanApplicationsDirectory
FNDA:5,(anonymous_7)
FNDA:3,(anonymous_8)
FNDA:0,determinePackageInfo
FNDA:1,checkAlreadyInstalled
FNDA:0,(anonymous_11)
FNDA:0,discoverApps
FNDA:0,(anonymous_13)
DA:5,2
DA:6,2
DA:7,2
DA:8,2
DA:10,2
DA:17,2
DA:23,2
DA:30,2
DA:36,2
DA:37,2
DA:38,2
DA:45,0
DA:57,2
DA:58,0
DA:59,0
DA:65,2
DA:66,1
DA:67,1
DA:68,0
DA:70,1
DA:76,2
DA:77,1
DA:78,1
DA:79,0
DA:81,1
DA:87,2
DA:88,0
DA:89,0
DA:95,2
DA:96,0
DA:97,0
DA:103,2
DA:104,5
DA:105,5
DA:107,1
DA:108,5
DA:109,3
DA:111,4
DA:112,2
DA:118,2
DA:119,2
DA:125,0
DA:136,2
DA:141,0
DA:142,0
DA:143,0
DA:147,0
DA:148,0
DA:149,0
DA:152,0
DA:158,2
DA:159,1
DA:164,1
DA:165,1
DA:167,1
DA:183,2
DA:184,0
DA:186,0
DA:189,0
DA:190,0
DA:191,0
DA:197,0
DA:200,0
DA:201,0
DA:203,0
DA:204,0
DA:205,0
DA:208,0
DA:211,0
DA:212,0
DA:214,0
DA:215,0
DA:216,0
DA:219,0
DA:220,0
DA:228,0
DA:231,0
DA:233,0
DA:234,0
DA:236,0
DA:245,0
DA:246,0
DA:258,0
DA:259,0
DA:261,0
DA:263,0
LF:86
LH:39
BRDA:35,0,0,2
BRDA:46,1,0,0
BRDA:46,1,1,0
BRDA:47,2,0,0
BRDA:47,2,1,0
BRDA:48,3,0,0
BRDA:48,3,1,0
BRDA:48,3,2,0
BRDA:67,4,0,0
BRDA:78,5,0,0
BRDA:103,6,0,0
BRDA:108,7,0,5
BRDA:108,7,1,4
BRDA:111,8,0,2
BRDA:118,9,0,2
BRDA:142,10,0,0
BRDA:148,11,0,0
BRDA:170,12,0,0
BRDA:170,12,1,0
BRDA:170,12,2,0
BRDA:170,12,3,0
BRDA:173,13,0,0
BRDA:173,13,1,0
BRDA:174,14,0,0
BRDA:174,14,1,0
BRDA:174,14,2,0
BRDA:174,14,3,0
BRDA:190,15,0,0
BRDA:203,16,0,0
BRDA:219,17,0,0
BRDA:241,18,0,0
BRDA:241,18,1,0
BRF:32
BRH:5
end_of_record
TN:
SF:src/cli.ts
FN:15,getPackageVersion
FN:28,createProgram
FN:84,parseArguments
FN:108,(anonymous_3)
FN:138,displayWelcome
FN:164,displayTroubleshooting
FN:194,setupSignalHandlers
FN:197,(anonymous_7)
FN:202,(anonymous_8)
FN:207,(anonymous_9)
FN:215,(anonymous_10)
FN:224,validateEnvironment
FNF:12
FNH:5
FNDA:15,getPackageVersion
FNDA:15,createProgram
FNDA:11,parseArguments
FNDA:9,(anonymous_3)
FNDA:0,displayWelcome
FNDA:0,displayTroubleshooting
FNDA:0,setupSignalHandlers
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,(anonymous_10)
FNDA:3,validateEnvironment
DA:5,2
DA:6,2
DA:7,2
DA:9,2
DA:10,2
DA:16,15
DA:17,15
DA:18,15
DA:19,13
DA:21,2
DA:28,2
DA:29,15
DA:31,15
DA:38,15
DA:61,15
DA:78,15
DA:84,2
DA:85,11
DA:87,11
DA:88,11
DA:89,11
DA:92,11
DA:93,0
DA:97,11
DA:101,11
DA:102,10
DA:103,1
DA:107,10
DA:108,9
DA:114,10
DA:116,1
DA:118,1
DA:120,0
DA:123,1
DA:125,0
DA:129,1
DA:130,1
DA:131,1
DA:138,2
DA:139,0
DA:141,0
DA:142,0
DA:144,0
DA:145,0
DA:148,0
DA:150,0
DA:151,0
DA:154,0
DA:155,0
DA:158,0
DA:164,2
DA:165,0
DA:166,0
DA:167,0
DA:194,2
DA:195,0
DA:197,0
DA:198,0
DA:199,0
DA:202,0
DA:203,0
DA:204,0
DA:207,0
DA:208,0
DA:209,0
DA:210,0
DA:212,0
DA:215,0
DA:216,0
DA:217,0
DA:224,2
DA:225,3
DA:228,3
DA:229,3
DA:231,3
DA:232,1
DA:233,1
DA:237,2
DA:238,1
DA:239,1
DA:242,1
LF:81
LH:49
BRDA:19,0,0,13
BRDA:19,0,1,0
BRDA:84,1,0,0
BRDA:92,2,0,0
BRDA:92,3,0,11
BRDA:92,3,1,11
BRDA:97,4,0,11
BRDA:97,4,1,0
BRDA:98,5,0,0
BRDA:98,5,1,0
BRDA:102,6,0,1
BRDA:102,7,0,10
BRDA:102,7,1,10
BRDA:111,8,0,10
BRDA:111,8,1,0
BRDA:118,9,0,0
BRDA:123,10,0,0
BRDA:144,11,0,0
BRDA:150,12,0,0
BRDA:150,13,0,0
BRDA:150,13,1,0
BRDA:154,14,0,0
BRDA:209,15,0,0
BRDA:229,16,0,3
BRDA:229,16,1,0
BRDA:231,17,0,1
BRDA:237,18,0,1
BRF:27
BRH:11
end_of_record
TN:
SF:src/constants.ts
FN:19,(anonymous_0)
FN:21,(anonymous_1)
FN:23,(anonymous_2)
FN:23,(anonymous_3)
FN:25,(anonymous_4)
FN:25,(anonymous_5)
FNF:6
FNH:2
FNDA:0,(anonymous_0)
FNDA:0,(anonymous_1)
FNDA:1,(anonymous_2)
FNDA:1,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
DA:8,11
DA:13,11
DA:19,0
DA:21,0
DA:23,1
DA:25,0
DA:33,11
DA:43,11
DA:55,11
DA:69,11
DA:85,11
DA:102,11
LF:12
LH:9
BRF:0
BRH:0
end_of_record
TN:
SF:src/error-handler.ts
FN:16,(anonymous_0)
FN:24,(anonymous_1)
FN:34,(anonymous_2)
FN:75,(anonymous_3)
FN:96,(anonymous_4)
FN:105,(anonymous_5)
FN:113,(anonymous_6)
FN:121,(anonymous_7)
FN:129,(anonymous_8)
FN:137,(anonymous_9)
FN:154,(anonymous_10)
FN:163,(anonymous_11)
FN:177,(anonymous_12)
FN:208,(anonymous_13)
FN:222,(anonymous_14)
FN:239,initializeErrorHandler
FN:247,getErrorHandler
FN:257,setupGlobalErrorHandlers
FN:260,(anonymous_18)
FN:265,(anonymous_19)
FN:275,createProgressCallback
FN:276,(anonymous_21)
FNF:22
FNH:20
FNDA:22,(anonymous_0)
FNDA:10,(anonymous_1)
FNDA:7,(anonymous_2)
FNDA:3,(anonymous_3)
FNDA:1,(anonymous_4)
FNDA:2,(anonymous_5)
FNDA:1,(anonymous_6)
FNDA:2,(anonymous_7)
FNDA:2,(anonymous_8)
FNDA:1,(anonymous_9)
FNDA:18,(anonymous_10)
FNDA:26,(anonymous_11)
FNDA:6,(anonymous_12)
FNDA:24,(anonymous_13)
FNDA:3,(anonymous_14)
FNDA:12,initializeErrorHandler
FNDA:2,getErrorHandler
FNDA:11,setupGlobalErrorHandlers
FNDA:0,(anonymous_18)
FNDA:0,(anonymous_19)
FNDA:1,createProgressCallback
FNDA:1,(anonymous_21)
DA:5,2
DA:6,2
DA:7,2
DA:12,2
DA:17,22
DA:18,22
DA:25,10
DA:27,10
DA:28,7
DA:30,3
DA:35,7
DA:37,1
DA:38,1
DA:39,1
DA:42,1
DA:43,1
DA:44,1
DA:47,1
DA:48,1
DA:49,1
DA:52,2
DA:53,2
DA:54,2
DA:57,1
DA:58,1
DA:59,1
DA:62,1
DA:63,1
DA:64,1
DA:67,0
DA:68,0
DA:69,0
DA:71,0
DA:76,3
DA:79,3
DA:80,1
DA:81,2
DA:82,1
DA:83,1
DA:84,0
DA:85,1
DA:86,0
DA:89,3
DA:90,0
DA:93,3
DA:97,1
DA:98,1
DA:99,1
DA:100,1
DA:101,1
DA:102,1
DA:106,2
DA:107,2
DA:108,2
DA:109,2
DA:110,2
DA:114,1
DA:115,1
DA:116,1
DA:117,1
DA:118,1
DA:122,2
DA:123,2
DA:124,2
DA:125,2
DA:126,2
DA:130,2
DA:131,2
DA:132,2
DA:133,2
DA:134,2
DA:138,1
DA:139,1
DA:140,1
DA:141,1
DA:142,1
DA:149,2
DA:155,18
DA:156,18
DA:157,18
DA:164,26
DA:165,26
DA:167,26
DA:168,4
DA:170,22
DA:178,6
DA:179,6
DA:180,6
DA:183,6
DA:184,2
DA:187,4
DA:189,4
DA:191,4
DA:192,3
DA:193,3
DA:194,3
DA:197,4
DA:198,0
DA:199,0
DA:202,4
DA:209,24
DA:210,24
DA:212,24
DA:213,22
DA:215,2
DA:223,3
DA:224,3
DA:225,3
DA:227,3
DA:234,2
DA:239,2
DA:240,12
DA:241,12
DA:247,2
DA:248,2
DA:249,0
DA:251,2
DA:257,2
DA:258,11
DA:260,11
DA:261,0
DA:262,0
DA:265,11
DA:266,0
DA:267,0
DA:268,0
DA:275,2
DA:276,1
DA:277,1
LF:129
LH:114
BRDA:16,0,0,0
BRDA:25,1,0,1
BRDA:25,1,1,9
BRDA:27,2,0,7
BRDA:27,2,1,3
BRDA:35,3,0,1
BRDA:35,3,1,1
BRDA:35,3,2,1
BRDA:35,3,3,2
BRDA:35,3,4,1
BRDA:35,3,5,1
BRDA:35,3,6,0
BRDA:68,4,0,0
BRDA:68,5,0,0
BRDA:68,5,1,0
BRDA:79,6,0,1
BRDA:79,6,1,2
BRDA:81,7,0,1
BRDA:81,7,1,1
BRDA:83,8,0,0
BRDA:83,8,1,1
BRDA:85,9,0,0
BRDA:85,10,0,1
BRDA:85,10,1,0
BRDA:89,11,0,0
BRDA:154,12,0,0
BRDA:167,13,0,4
BRDA:167,13,1,22
BRDA:183,14,0,2
BRDA:183,15,0,6
BRDA:183,15,1,3
BRDA:191,16,0,3
BRDA:191,17,0,4
BRDA:191,17,1,3
BRDA:197,18,0,0
BRDA:208,19,0,19
BRDA:212,20,0,22
BRDA:212,20,1,2
BRDA:222,21,0,3
BRDA:239,22,0,0
BRDA:248,23,0,0
BRDA:257,24,0,0
BRDA:267,25,0,0
BRDA:267,25,1,0
BRF:44
BRH:28
end_of_record
TN:
SF:src/index.ts
FN:42,createScannerConfig
FN:53,createInstallerConfig
FN:69,generateOperationSummary
FN:75,(anonymous_3)
FN:76,(anonymous_4)
FN:77,(anonymous_5)
FN:78,(anonymous_6)
FN:96,handleError
FN:136,main
FN:206,(anonymous_9)
FN:207,(anonymous_10)
FN:209,(anonymous_11)
FN:210,(anonymous_12)
FN:252,(anonymous_13)
FNF:14
FNH:13
FNDA:10,createScannerConfig
FNDA:3,createInstallerConfig
FNDA:3,generateOperationSummary
FNDA:3,(anonymous_3)
FNDA:3,(anonymous_4)
FNDA:3,(anonymous_5)
FNDA:3,(anonymous_6)
FNDA:9,handleError
FNDA:10,main
FNDA:3,(anonymous_9)
FNDA:2,(anonymous_10)
FNDA:3,(anonymous_11)
FNDA:1,(anonymous_12)
FNDA:0,(anonymous_13)
DA:8,1
DA:15,1
DA:16,1
DA:23,1
DA:24,1
DA:32,1
DA:33,1
DA:34,1
DA:43,10
DA:54,3
DA:59,3
DA:60,1
DA:63,3
DA:75,3
DA:76,3
DA:77,3
DA:78,3
DA:80,3
DA:97,9
DA:98,0
DA:100,0
DA:101,0
DA:102,0
DA:105,0
DA:106,0
DA:107,0
DA:110,0
DA:111,0
DA:114,0
DA:115,0
DA:118,0
DA:119,0
DA:120,0
DA:122,0
DA:125,9
DA:126,9
DA:127,9
DA:129,9
DA:137,10
DA:139,10
DA:142,10
DA:145,10
DA:146,10
DA:149,10
DA:150,10
DA:153,10
DA:156,10
DA:157,10
DA:158,10
DA:161,10
DA:162,10
DA:163,10
DA:164,10
DA:165,9
DA:167,9
DA:168,3
DA:169,3
DA:173,6
DA:175,5
DA:176,1
DA:177,1
DA:178,1
DA:182,4
DA:185,4
DA:188,4
DA:189,4
DA:190,1
DA:191,1
DA:195,3
DA:196,3
DA:197,3
DA:198,3
DA:199,3
DA:200,3
DA:203,3
DA:206,3
DA:207,3
DA:209,3
DA:210,3
DA:213,3
DA:216,3
DA:223,3
DA:226,3
DA:227,1
DA:228,1
DA:230,2
DA:231,2
DA:235,10
DA:238,10
DA:239,1
DA:240,1
DA:244,9
DA:251,1
DA:252,0
DA:253,0
DA:254,0
DA:257,0
DA:260,0
DA:263,0
DA:268,1
LF:100
LH:79
BRDA:44,0,0,10
BRDA:44,0,1,0
BRDA:45,1,0,10
BRDA:45,1,1,0
BRDA:46,2,0,10
BRDA:46,2,1,9
BRDA:55,3,0,3
BRDA:55,3,1,2
BRDA:56,4,0,3
BRDA:56,4,1,3
BRDA:59,5,0,1
BRDA:97,6,0,0
BRDA:97,6,1,9
BRDA:98,7,0,0
BRDA:98,7,1,0
BRDA:98,7,2,0
BRDA:98,7,3,0
BRDA:98,7,4,0
BRDA:119,8,0,0
BRDA:119,9,0,0
BRDA:119,9,1,0
BRDA:126,10,0,9
BRDA:146,11,0,10
BRDA:146,11,1,9
BRDA:149,12,0,10
BRDA:149,12,1,9
BRDA:150,13,0,10
BRDA:150,13,1,9
BRDA:167,14,0,3
BRDA:175,15,0,1
BRDA:189,16,0,1
BRDA:195,17,0,1
BRDA:195,17,1,2
BRDA:197,18,0,1
BRDA:197,18,1,2
BRDA:220,19,0,3
BRDA:220,19,1,2
BRDA:226,20,0,1
BRDA:226,20,1,2
BRDA:238,21,0,1
BRDA:251,22,0,0
BRDA:257,23,0,0
BRDA:257,24,0,0
BRDA:257,24,1,0
BRDA:257,24,2,0
BRF:45
BRH:29
end_of_record
TN:
SF:src/installer.ts
FN:26,executeCommand
FN:61,executeSudoCommand
FN:99,installCasks
FN:108,(anonymous_3)
FN:118,(anonymous_4)
FN:128,(anonymous_5)
FN:141,installFormulas
FN:150,(anonymous_7)
FN:160,(anonymous_8)
FN:168,(anonymous_9)
FN:181,deleteOriginalApps
FN:188,(anonymous_11)
FN:198,(anonymous_12)
FN:236,installApps
FN:255,(anonymous_14)
FN:284,(anonymous_15)
FN:285,(anonymous_16)
FN:312,validateInstallationPrerequisites
FN:327,getInstallationSummary
FN:340,(anonymous_19)
FN:347,(anonymous_20)
FNF:21
FNH:11
FNDA:1,executeCommand
FNDA:0,executeSudoCommand
FNDA:1,installCasks
FNDA:1,(anonymous_3)
FNDA:1,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,installFormulas
FNDA:0,(anonymous_7)
FNDA:0,(anonymous_8)
FNDA:0,(anonymous_9)
FNDA:0,deleteOriginalApps
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:2,installApps
FNDA:1,(anonymous_14)
FNDA:1,(anonymous_15)
FNDA:1,(anonymous_16)
FNDA:0,validateInstallationPrerequisites
FNDA:4,getInstallationSummary
FNDA:2,(anonymous_19)
FNDA:1,(anonymous_20)
DA:5,2
DA:6,2
DA:7,2
DA:8,2
DA:18,2
DA:19,2
DA:21,2
DA:31,1
DA:32,1
DA:40,0
DA:41,0
DA:42,0
DA:49,0
DA:67,0
DA:68,0
DA:76,0
DA:78,0
DA:79,0
DA:80,0
DA:87,0
DA:104,1
DA:105,0
DA:108,1
DA:109,1
DA:111,1
DA:112,1
DA:114,1
DA:116,1
DA:117,1
DA:118,1
DA:125,0
DA:128,0
DA:146,0
DA:147,0
DA:150,0
DA:151,0
DA:153,0
DA:154,0
DA:156,0
DA:158,0
DA:159,0
DA:160,0
DA:167,0
DA:168,0
DA:188,0
DA:190,0
DA:191,0
DA:192,0
DA:195,0
DA:197,0
DA:198,0
DA:199,0
DA:200,0
DA:201,0
DA:204,0
DA:205,0
DA:206,0
DA:207,0
DA:211,0
DA:212,0
DA:214,0
DA:215,0
DA:219,0
DA:220,0
DA:222,0
DA:223,0
DA:225,0
DA:228,0
DA:236,2
DA:240,2
DA:242,2
DA:243,1
DA:244,1
DA:255,1
DA:256,1
DA:257,1
DA:259,1
DA:261,1
DA:263,1
DA:265,1
DA:266,1
DA:267,1
DA:270,1
DA:271,0
DA:272,1
DA:273,0
DA:278,1
DA:279,0
DA:280,0
DA:284,1
DA:285,1
DA:287,1
DA:289,1
DA:299,0
DA:301,0
DA:312,2
DA:314,0
DA:316,0
DA:317,0
DA:327,2
DA:328,4
DA:330,4
DA:331,1
DA:332,1
DA:334,3
DA:335,3
DA:338,4
DA:339,2
DA:340,2
DA:341,2
DA:345,4
DA:346,1
DA:347,1
DA:348,1
DA:352,4
DA:353,1
DA:356,4
LF:117
LH:58
BRDA:28,0,0,0
BRDA:29,1,0,1
BRDA:31,2,0,1
BRDA:50,3,0,0
BRDA:50,3,1,0
BRDA:51,4,0,0
BRDA:51,4,1,0
BRDA:52,5,0,0
BRDA:52,5,1,0
BRDA:52,5,2,0
BRDA:64,6,0,0
BRDA:65,7,0,0
BRDA:67,8,0,0
BRDA:88,9,0,0
BRDA:88,9,1,0
BRDA:89,10,0,0
BRDA:89,10,1,0
BRDA:90,11,0,0
BRDA:90,11,1,0
BRDA:90,11,2,0
BRDA:104,12,0,0
BRDA:111,13,0,1
BRDA:111,13,1,0
BRDA:116,14,0,1
BRDA:116,14,1,0
BRDA:146,15,0,0
BRDA:153,16,0,0
BRDA:153,16,1,0
BRDA:158,17,0,0
BRDA:158,17,1,0
BRDA:190,18,0,0
BRDA:195,19,0,0
BRDA:195,19,1,0
BRDA:199,20,0,0
BRDA:205,21,0,0
BRDA:222,22,0,0
BRDA:222,22,1,0
BRDA:242,23,0,1
BRDA:256,24,0,1
BRDA:256,24,1,0
BRDA:257,25,0,1
BRDA:257,25,1,1
BRDA:265,26,0,1
BRDA:270,27,0,0
BRDA:270,27,1,1
BRDA:270,28,0,1
BRDA:270,28,1,1
BRDA:272,29,0,0
BRDA:272,30,0,1
BRDA:272,30,1,1
BRDA:272,30,2,0
BRDA:278,31,0,0
BRDA:316,32,0,0
BRDA:330,33,0,1
BRDA:330,33,1,3
BRDA:338,34,0,2
BRDA:345,35,0,1
BRDA:348,36,0,1
BRDA:348,36,1,0
BRDA:352,37,0,1
BRDA:352,38,0,4
BRDA:352,38,1,2
BRF:62
BRH:22
end_of_record
TN:
SF:src/prompts.ts
FN:13,createAppChoices
FN:15,(anonymous_2)
FN:16,(anonymous_3)
FN:32,displayAppSummary
FN:34,(anonymous_5)
FN:35,(anonymous_6)
FN:36,(anonymous_7)
FN:37,(anonymous_8)
FN:43,(anonymous_9)
FN:44,(anonymous_10)
FN:58,(anonymous_11)
FN:65,(anonymous_12)
FN:72,(anonymous_13)
FN:96,promptAppSelection
FN:105,(anonymous_15)
FN:146,needsSudoPassword
FN:147,(anonymous_17)
FN:153,promptSudoPassword
FN:161,(anonymous_19)
FN:166,(anonymous_20)
FN:195,displayInstallationPlan
FN:204,(anonymous_22)
FN:205,(anonymous_23)
FN:212,(anonymous_24)
FN:223,(anonymous_25)
FN:237,promptConfirmation
FN:266,displayFinalSummary
FN:277,(anonymous_28)
FN:278,(anonymous_29)
FN:289,(anonymous_30)
FN:294,(anonymous_31)
FNF:31
FNH:28
FNDA:4,createAppChoices
FNDA:6,(anonymous_2)
FNDA:6,(anonymous_3)
FNDA:5,displayAppSummary
FNDA:12,(anonymous_5)
FNDA:12,(anonymous_6)
FNDA:12,(anonymous_7)
FNDA:12,(anonymous_8)
FNDA:6,(anonymous_9)
FNDA:6,(anonymous_10)
FNDA:0,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:5,promptAppSelection
FNDA:12,(anonymous_15)
FNDA:4,needsSudoPassword
FNDA:4,(anonymous_17)
FNDA:4,promptSudoPassword
FNDA:4,(anonymous_19)
FNDA:3,(anonymous_20)
FNDA:4,displayInstallationPlan
FNDA:4,(anonymous_22)
FNDA:4,(anonymous_23)
FNDA:3,(anonymous_24)
FNDA:1,(anonymous_25)
FNDA:2,promptConfirmation
FNDA:4,displayFinalSummary
FNDA:2,(anonymous_28)
FNDA:2,(anonymous_29)
FNDA:2,(anonymous_30)
FNDA:1,(anonymous_31)
DA:5,2
DA:6,2
DA:8,2
DA:14,4
DA:15,6
DA:17,6
DA:18,6
DA:20,6
DA:34,12
DA:35,12
DA:36,12
DA:37,12
DA:39,5
DA:40,5
DA:42,5
DA:43,6
DA:44,6
DA:46,4
DA:47,4
DA:48,4
DA:50,4
DA:51,1
DA:55,5
DA:56,2
DA:57,2
DA:58,0
DA:62,5
DA:63,2
DA:64,2
DA:65,0
DA:69,5
DA:70,2
DA:71,2
DA:72,0
DA:76,5
DA:77,1
DA:78,1
DA:79,1
DA:80,0
DA:81,0
DA:83,1
DA:86,4
DA:87,4
DA:88,4
DA:89,4
DA:90,4
DA:96,2
DA:100,5
DA:103,5
DA:105,12
DA:107,5
DA:108,1
DA:109,1
DA:113,4
DA:115,4
DA:116,4
DA:118,4
DA:126,3
DA:127,1
DA:128,1
DA:131,2
DA:132,2
DA:135,1
DA:136,1
DA:137,1
DA:139,0
DA:147,4
DA:153,2
DA:154,4
DA:156,4
DA:157,1
DA:158,1
DA:161,4
DA:163,3
DA:164,3
DA:165,3
DA:166,3
DA:167,3
DA:169,3
DA:170,3
DA:175,2
DA:176,1
DA:177,1
DA:180,1
DA:181,1
DA:184,1
DA:185,1
DA:186,1
DA:188,0
DA:195,2
DA:200,4
DA:201,1
DA:204,4
DA:205,4
DA:207,3
DA:208,3
DA:210,3
DA:211,3
DA:212,3
DA:214,3
DA:215,2
DA:217,1
DA:221,3
DA:222,1
DA:223,1
DA:224,1
DA:227,3
DA:228,1
DA:230,2
DA:237,2
DA:238,2
DA:240,2
DA:241,2
DA:247,2
DA:251,2
DA:252,2
DA:255,0
DA:256,0
DA:257,0
DA:259,0
DA:266,2
DA:272,4
DA:273,4
DA:275,4
DA:276,1
DA:277,2
DA:278,2
DA:280,1
DA:281,1
DA:283,1
DA:284,1
DA:287,3
DA:288,1
DA:289,2
DA:292,3
DA:293,1
DA:294,1
DA:297,3
DA:298,1
DA:302,4
LF:140
LH:129
BRDA:17,0,0,5
BRDA:17,0,1,1
BRDA:42,1,0,4
BRDA:47,2,0,4
BRDA:50,3,0,1
BRDA:55,4,0,2
BRDA:57,5,0,0
BRDA:62,6,0,2
BRDA:64,7,0,0
BRDA:69,8,0,2
BRDA:71,9,0,0
BRDA:76,10,0,1
BRDA:78,11,0,1
BRDA:78,11,1,0
BRDA:80,12,0,0
BRDA:100,13,0,5
BRDA:100,13,1,5
BRDA:107,14,0,1
BRDA:126,15,0,1
BRDA:135,16,0,1
BRDA:156,17,0,1
BRDA:165,18,0,3
BRDA:165,18,1,0
BRDA:175,19,0,1
BRDA:175,20,0,2
BRDA:175,20,1,1
BRDA:184,21,0,1
BRDA:198,22,0,0
BRDA:200,23,0,1
BRDA:207,24,0,1
BRDA:207,24,1,2
BRDA:210,25,0,3
BRDA:214,26,0,2
BRDA:214,26,1,1
BRDA:221,27,0,1
BRDA:227,28,0,1
BRDA:227,28,1,2
BRDA:237,29,0,0
BRDA:241,30,0,1
BRDA:241,30,1,1
BRDA:255,31,0,0
BRDA:270,32,0,0
BRDA:272,33,0,1
BRDA:272,33,1,3
BRDA:275,34,0,1
BRDA:275,34,1,3
BRDA:280,35,0,1
BRDA:283,36,0,1
BRDA:287,37,0,1
BRDA:292,38,0,1
BRDA:297,39,0,1
BRDA:297,40,0,3
BRDA:297,40,1,2
BRF:53
BRH:43
end_of_record
TN:
SF:src/types.ts
FN:136,(anonymous_0)
FN:150,(anonymous_1)
FNF:2
FNH:2
FNDA:7,(anonymous_0)
FNDA:13,(anonymous_1)
DA:136,7
DA:137,7
DA:138,7
DA:139,7
DA:140,7
DA:141,7
DA:142,7
DA:143,7
DA:149,7
DA:152,13
DA:153,13
DA:155,13
DA:156,13
LF:13
LH:13
BRDA:136,0,0,7
BRDA:136,0,1,7
BRF:2
BRH:2
end_of_record
TN:
SF:src/utils.ts
FN:12,normalizeAppName
FN:24,extractAppName
FN:32,isValidBrewPackageName
FN:39,isValidAppName
FN:46,colorize
FN:53,formatList
FN:54,(anonymous_6)
FN:60,createLogger
FN:62,(anonymous_8)
FN:65,(anonymous_9)
FN:68,(anonymous_10)
FN:71,(anonymous_11)
FN:76,(anonymous_12)
FN:87,sleep
FN:88,(anonymous_14)
FN:94,escapeShellArg
FN:101,parseCommandOutput
FN:104,(anonymous_17)
FN:105,(anonymous_18)
FN:111,isEmpty
FN:118,capitalize
FN:125,pluralize
FN:132,formatDuration
FN:150,createProgressBar
FN:164,truncate
FN:174,groupBy
FN:178,(anonymous_26)
FN:191,uniqueBy
FN:193,(anonymous_28)
FNF:29
FNH:26
FNDA:9,normalizeAppName
FNDA:5,extractAppName
FNDA:8,isValidBrewPackageName
FNDA:6,isValidAppName
FNDA:202,colorize
FNDA:12,formatList
FNDA:14,(anonymous_6)
FNDA:77,createLogger
FNDA:80,(anonymous_8)
FNDA:10,(anonymous_9)
FNDA:22,(anonymous_10)
FNDA:9,(anonymous_11)
FNDA:9,(anonymous_12)
FNDA:0,sleep
FNDA:0,(anonymous_14)
FNDA:0,escapeShellArg
FNDA:4,parseCommandOutput
FNDA:149,(anonymous_17)
FNDA:149,(anonymous_18)
FNDA:6,isEmpty
FNDA:3,capitalize
FNDA:17,pluralize
FNDA:3,formatDuration
FNDA:1,createProgressBar
FNDA:2,truncate
FNDA:2,groupBy
FNDA:4,(anonymous_26)
FNDA:1,uniqueBy
FNDA:3,(anonymous_28)
DA:5,11
DA:12,11
DA:13,9
DA:24,11
DA:25,5
DA:26,5
DA:32,11
DA:33,8
DA:39,11
DA:40,6
DA:46,11
DA:47,202
DA:53,11
DA:54,14
DA:60,11
DA:61,77
DA:63,80
DA:66,10
DA:69,22
DA:72,9
DA:73,0
DA:77,9
DA:78,0
DA:87,11
DA:88,0
DA:94,11
DA:95,0
DA:101,11
DA:102,4
DA:104,149
DA:105,149
DA:111,11
DA:112,6
DA:118,11
DA:119,3
DA:125,11
DA:126,17
DA:132,11
DA:133,3
DA:134,1
DA:137,2
DA:138,2
DA:139,1
DA:142,1
DA:143,1
DA:144,1
DA:150,11
DA:151,1
DA:152,1
DA:153,1
DA:155,1
DA:156,1
DA:158,1
DA:164,11
DA:165,2
DA:166,1
DA:168,1
DA:174,11
DA:178,2
DA:179,4
DA:180,4
DA:181,3
DA:183,4
DA:184,4
DA:191,11
DA:192,1
DA:193,1
DA:194,3
DA:195,3
DA:196,1
DA:198,2
DA:199,2
LF:72
LH:68
BRDA:25,0,0,5
BRDA:25,0,1,1
BRDA:40,1,0,6
BRDA:40,1,1,4
BRDA:53,2,0,11
BRDA:60,3,0,0
BRDA:72,4,0,0
BRDA:77,5,0,0
BRDA:112,6,0,6
BRDA:112,6,1,3
BRDA:125,7,0,16
BRDA:126,8,0,11
BRDA:126,8,1,6
BRDA:133,9,0,1
BRDA:138,10,0,1
BRDA:150,11,0,0
BRDA:165,12,0,1
BRDA:180,13,0,3
BRDA:195,14,0,1
BRF:19
BRH:15
end_of_record
