
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.18% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>589/794</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">54.57% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>155/284</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">75.49% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>114/151</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.4% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>558/750</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="app-scanner.ts"><a href="app-scanner.ts.html">app-scanner.ts</a></td>
	<td data-value="44.31" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 44%"></div><div class="cover-empty" style="width: 56%"></div></div>
	</td>
	<td data-value="44.31" class="pct low">44.31%</td>
	<td data-value="88" class="abs low">39/88</td>
	<td data-value="15.62" class="pct low">15.62%</td>
	<td data-value="32" class="abs low">5/32</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="14" class="abs medium">7/14</td>
	<td data-value="45.34" class="pct low">45.34%</td>
	<td data-value="86" class="abs low">39/86</td>
	</tr>

<tr>
	<td class="file medium" data-value="cli.ts"><a href="cli.ts.html">cli.ts</a></td>
	<td data-value="60.49" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 60%"></div><div class="cover-empty" style="width: 40%"></div></div>
	</td>
	<td data-value="60.49" class="pct medium">60.49%</td>
	<td data-value="81" class="abs medium">49/81</td>
	<td data-value="40.74" class="pct low">40.74%</td>
	<td data-value="27" class="abs low">11/27</td>
	<td data-value="41.66" class="pct low">41.66%</td>
	<td data-value="12" class="abs low">5/12</td>
	<td data-value="60.49" class="pct medium">60.49%</td>
	<td data-value="81" class="abs medium">49/81</td>
	</tr>

<tr>
	<td class="file medium" data-value="constants.ts"><a href="constants.ts.html">constants.ts</a></td>
	<td data-value="71.42" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="14" class="abs medium">10/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="12" class="abs medium">9/12</td>
	</tr>

<tr>
	<td class="file high" data-value="error-handler.ts"><a href="error-handler.ts.html">error-handler.ts</a></td>
	<td data-value="88.37" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.37" class="pct high">88.37%</td>
	<td data-value="129" class="abs high">114/129</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="44" class="abs medium">28/44</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="22" class="abs high">20/22</td>
	<td data-value="88.37" class="pct high">88.37%</td>
	<td data-value="129" class="abs high">114/129</td>
	</tr>

<tr>
	<td class="file high" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="80.18" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80.18" class="pct high">80.18%</td>
	<td data-value="106" class="abs high">85/106</td>
	<td data-value="64.44" class="pct medium">64.44%</td>
	<td data-value="45" class="abs medium">29/45</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	<td data-value="79" class="pct medium">79%</td>
	<td data-value="100" class="abs medium">79/100</td>
	</tr>

<tr>
	<td class="file low" data-value="installer.ts"><a href="installer.ts.html">installer.ts</a></td>
	<td data-value="49.21" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 49%"></div><div class="cover-empty" style="width: 51%"></div></div>
	</td>
	<td data-value="49.21" class="pct low">49.21%</td>
	<td data-value="128" class="abs low">63/128</td>
	<td data-value="35.48" class="pct low">35.48%</td>
	<td data-value="62" class="abs low">22/62</td>
	<td data-value="52.38" class="pct medium">52.38%</td>
	<td data-value="21" class="abs medium">11/21</td>
	<td data-value="49.57" class="pct low">49.57%</td>
	<td data-value="117" class="abs low">58/117</td>
	</tr>

<tr>
	<td class="file high" data-value="prompts.ts"><a href="prompts.ts.html">prompts.ts</a></td>
	<td data-value="91.3" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 91%"></div><div class="cover-empty" style="width: 9%"></div></div>
	</td>
	<td data-value="91.3" class="pct high">91.3%</td>
	<td data-value="161" class="abs high">147/161</td>
	<td data-value="81.13" class="pct high">81.13%</td>
	<td data-value="53" class="abs high">43/53</td>
	<td data-value="90.32" class="pct high">90.32%</td>
	<td data-value="31" class="abs high">28/31</td>
	<td data-value="92.14" class="pct high">92.14%</td>
	<td data-value="140" class="abs high">129/140</td>
	</tr>

<tr>
	<td class="file high" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	</tr>

<tr>
	<td class="file high" data-value="utils.ts"><a href="utils.ts.html">utils.ts</a></td>
	<td data-value="93.24" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 93%"></div><div class="cover-empty" style="width: 7%"></div></div>
	</td>
	<td data-value="93.24" class="pct high">93.24%</td>
	<td data-value="74" class="abs high">69/74</td>
	<td data-value="78.94" class="pct medium">78.94%</td>
	<td data-value="19" class="abs medium">15/19</td>
	<td data-value="89.65" class="pct high">89.65%</td>
	<td data-value="29" class="abs high">26/29</td>
	<td data-value="94.44" class="pct high">94.44%</td>
	<td data-value="72" class="abs high">68/72</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T18:54:00.142Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    