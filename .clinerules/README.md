# Cline Rules System for convert-apps-to-homebrew

This directory contains Cline rules that provide LLM assistants with comprehensive guidance for working on this TypeScript CLI project.

## Active Rules

The following rules are currently active and will be automatically loaded by Cline:

### 01-project-overview.md
- **Purpose**: Core project understanding and context
- **Contains**: Project goals, technical stack, key features, file structure
- **When to reference**: Always - provides essential project context

### 02-coding-standards.md
- **Purpose**: TypeScript and code quality guidelines
- **Contains**: Type safety, naming conventions, module structure, error handling
- **When to reference**: When writing or modifying code

### 03-user-experience.md
- **Purpose**: User interface and experience guidelines
- **Contains**: UX principles, interactive interface standards, output formatting
- **When to reference**: When changing user-facing functionality

### 04-testing-strategy.md
- **Purpose**: Testing approach and quality assurance
- **Contains**: Test organization, mocking strategy, coverage requirements
- **When to reference**: When adding tests or modifying existing functionality

### 05-documentation.md
- **Purpose**: Documentation standards and maintenance
- **Contains**: README guidelines, API documentation, examples, maintenance docs
- **When to reference**: When updating documentation or adding features

### 06-llm-interaction.md
- **Purpose**: Guidelines for LLM assistants working on this project
- **Contains**: Context awareness, code modification guidelines, communication style
- **When to reference**: Always - provides meta-guidance for AI assistance

## Rules Bank

Additional specialized rules are available in the `clinerules-bank/` directory. These can be activated by copying them to the `.clinerules/` directory when needed:

### performance-optimization.md
- **Purpose**: Performance tuning and optimization guidelines
- **Activate when**: Working on performance improvements, optimizing CLI responsiveness
- **Contains**: Memory efficiency, network operations, command execution optimization

### security-hardening.md
- **Purpose**: Security best practices and vulnerability prevention
- **Activate when**: Reviewing security, handling user input, working with file operations
- **Contains**: Input validation, credential security, command injection prevention

### debugging-troubleshooting.md
- **Purpose**: Debugging strategies and troubleshooting procedures
- **Activate when**: Investigating bugs, adding diagnostic features, improving error handling
- **Contains**: Systematic debugging, common issues, diagnostic tools

## Usage Instructions

### For LLM Assistants

1. **Always read active rules first**: Understand the project context and guidelines
2. **Reference specific rules**: When working on particular areas, focus on relevant rules
3. **Follow established patterns**: Use existing code patterns and conventions
4. **Maintain consistency**: Ensure changes align with project standards

### For Developers

#### Activating Additional Rules
```bash
# Activate performance optimization rules
cp clinerules-bank/performance-optimization.md .clinerules/

# Activate security hardening rules
cp clinerules-bank/security-hardening.md .clinerules/

# Activate debugging rules
cp clinerules-bank/debugging-troubleshooting.md .clinerules/
```

#### Deactivating Rules
```bash
# Remove specific rule
rm .clinerules/performance-optimization.md

# Remove all optional rules (keep core rules)
rm .clinerules/performance-optimization.md
rm .clinerules/security-hardening.md
rm .clinerules/debugging-troubleshooting.md
```

#### Creating New Rules
```bash
# Create new rule in bank
touch clinerules-bank/new-feature.md

# Activate when needed
cp clinerules-bank/new-feature.md .clinerules/
```

## Rule Management

### Core Rules (Always Active)
- **01-project-overview.md**: Essential project context
- **02-coding-standards.md**: Code quality and TypeScript guidelines
- **03-user-experience.md**: User interface standards
- **04-testing-strategy.md**: Testing and quality assurance
- **05-documentation.md**: Documentation standards
- **06-llm-interaction.md**: LLM assistant guidelines

### Optional Rules (Activate as Needed)
- **performance-optimization.md**: For performance work
- **security-hardening.md**: For security reviews
- **debugging-troubleshooting.md**: For bug investigation

### Rule Combinations

#### Development Work
```bash
# Standard development (core rules only)
# No additional rules needed

# Performance-focused development
cp clinerules-bank/performance-optimization.md .clinerules/

# Security-focused development
cp clinerules-bank/security-hardening.md .clinerules/
```

#### Maintenance Work
```bash
# Bug fixing and troubleshooting
cp clinerules-bank/debugging-troubleshooting.md .clinerules/

# Security audit
cp clinerules-bank/security-hardening.md .clinerules/

# Performance optimization
cp clinerules-bank/performance-optimization.md .clinerules/
```

## Best Practices

### For LLM Assistants
1. **Read all active rules**: Understand the complete context before making changes
2. **Reference specific sections**: Quote relevant rule sections when explaining decisions
3. **Ask for clarification**: If rules conflict or are unclear, ask for guidance
4. **Suggest rule updates**: If you notice gaps or improvements needed in rules

### For Developers
1. **Keep core rules active**: Don't remove the essential project rules
2. **Activate relevant rules**: Use specialized rules when working in those areas
3. **Update rules with project**: Keep rules current as the project evolves
4. **Document rule changes**: Update this README when adding new rules

## Rule Maintenance

### Updating Rules
- Rules should be updated when project standards change
- New features may require new rules or rule updates
- Regular review ensures rules remain current and useful

### Adding New Rules
1. Create rule in `clinerules-bank/` directory
2. Test with LLM assistant to ensure effectiveness
3. Update this README with usage instructions
4. Consider if rule should be core (always active) or optional

### Rule Quality
- Rules should be specific and actionable
- Avoid conflicting guidance between rules
- Include examples and code snippets where helpful
- Keep rules focused on specific concerns

## Integration with Development Workflow

### Git Integration
- Core rules are tracked in git
- Optional rules can be git-ignored if desired
- Rule changes should be reviewed like code changes

### Team Consistency
- All team members should use the same core rules
- Optional rules can be activated based on individual tasks
- Document team conventions for rule usage

### CI/CD Integration
- Rules can be validated in CI/CD pipeline
- Automated checks can ensure rule compliance
- Documentation can be auto-generated from rules
