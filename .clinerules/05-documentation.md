# Documentation Standards

## Documentation Philosophy

### User-Centric Approach
- **Start with user goals**: What is the user trying to accomplish?
- **Provide context**: Explain why something is needed, not just how
- **Include examples**: Show real-world usage scenarios
- **Anticipate questions**: Address common concerns proactively

### Maintenance Strategy
- **Keep docs in sync**: Update documentation with every feature change
- **Version documentation**: Track changes in CHANGELOG.md
- **Review regularly**: Ensure accuracy and relevance
- **Test examples**: Verify all code examples actually work

## Documentation Types

### README.md - Primary User Documentation
**Purpose**: First impression and primary reference for users

**Required Sections**:
1. **Project description** with badges and key benefits
2. **Quick start** with installation and basic usage
3. **Features list** with clear value propositions
4. **Requirements** and prerequisites
5. **Usage examples** with expected output
6. **Command-line options** reference
7. **Troubleshooting** common issues
8. **Contributing** guidelines
9. **License** information

**Writing Style**:
- Use active voice and present tense
- Include emojis for visual appeal and scanning
- Provide copy-pasteable commands
- Show actual output examples
- Use consistent formatting and structure

### CHANGELOG.md - Version History
**Purpose**: Track all notable changes for users and maintainers

**Format**: Follow [Keep a Changelog](https://keepachangelog.com/) standard
- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** in case of vulnerabilities

**Content Guidelines**:
- Write for users, not developers
- Focus on impact and benefits
- Include migration guidance for breaking changes
- Link to relevant issues or PRs when helpful

### Code Documentation
**Purpose**: Help developers understand and maintain the code

**JSDoc Standards**:
```typescript
/**
 * Discovers applications in the specified directory and checks Homebrew availability
 * 
 * @param config - Scanner configuration with directory and ignore list
 * @returns Promise resolving to array of discovered apps with their status
 * @throws ConvertAppsError when directory is not accessible
 * 
 * @example
 * ```typescript
 * const apps = await discoverApps({
 *   applicationsDir: '/Applications',
 *   ignoredApps: ['Adobe Photoshop'],
 *   verbose: false
 * });
 * ```
 */
export async function discoverApps(config: ScannerConfig): Promise<AppInfo[]> {
  // Implementation
}
```

**Inline Comments**:
- Explain complex business logic
- Document non-obvious decisions
- Clarify performance considerations
- Note security implications

## API Documentation

### Type Definitions
**Purpose**: Clear contracts for data structures and interfaces

**Documentation Requirements**:
- Document all public interfaces
- Explain field purposes and constraints
- Provide usage examples
- Note optional vs required fields

```typescript
/**
 * Information about a discovered application
 */
export interface AppInfo {
  /** Original application name as found in filesystem */
  originalName: string;
  
  /** Normalized Homebrew package name */
  brewName: string;
  
  /** Full path to the .app bundle */
  appPath: string;
  
  /** Type of Homebrew package (cask for GUI apps, formula for CLI tools) */
  brewType: 'cask' | 'formula' | 'unavailable';
  
  /** Current status of the application */
  status: 'available' | 'already-installed' | 'ignored' | 'unavailable';
  
  /** Whether app is already managed by Homebrew */
  alreadyInstalled: boolean;
}
```

### Error Documentation
**Purpose**: Help users understand and recover from errors

**Error Reference Format**:
```markdown
## Error: HOMEBREW_NOT_INSTALLED

**Cause**: Homebrew is not installed or not accessible in PATH

**Solution**:
1. Install Homebrew: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`
2. Add to PATH: `echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile`
3. Restart terminal and try again

**Verification**: Run `brew --version` to confirm installation
```

## Examples and Tutorials

### Usage Examples
**Purpose**: Show real-world scenarios and expected outcomes

**Example Structure**:
1. **Scenario description**: What the user wants to accomplish
2. **Command**: Exact command to run
3. **Expected output**: What the user should see
4. **Explanation**: Why this approach works
5. **Variations**: Alternative approaches or options

### Tutorial Content
**Purpose**: Guide users through complete workflows

**Tutorial Guidelines**:
- Start with simplest use case
- Build complexity gradually
- Include screenshots or output examples
- Provide troubleshooting for each step
- End with next steps or advanced usage

## Maintenance Documentation

### PUBLISHING.md - Release Process
**Purpose**: Guide maintainers through publishing workflow

**Required Content**:
- Pre-publishing checklist
- Version management strategy
- Testing requirements
- Publishing commands
- Post-release verification
- Rollback procedures

### Development Setup
**Purpose**: Help contributors get started quickly

**Required Information**:
- Prerequisites and installation
- Development workflow
- Testing procedures
- Code style guidelines
- Contribution process

## Documentation Quality Standards

### Writing Guidelines
- **Clear and concise**: Use simple language and short sentences
- **Scannable**: Use headers, lists, and formatting for easy scanning
- **Actionable**: Provide specific steps users can follow
- **Complete**: Don't assume prior knowledge
- **Accurate**: Test all examples and keep them current

### Visual Elements
- **Code blocks**: Use syntax highlighting and proper formatting
- **Screenshots**: Include for complex UI interactions
- **Diagrams**: Use for architecture or workflow explanations
- **Badges**: Show build status, version, license, etc.

### Accessibility
- **Alt text**: Provide descriptions for images
- **Clear structure**: Use proper heading hierarchy
- **Link text**: Make link purposes clear from text alone
- **Color independence**: Don't rely solely on color for meaning

## Documentation Review Process

### Content Review
- **Accuracy**: Verify all technical information
- **Completeness**: Ensure all features are documented
- **Clarity**: Test with someone unfamiliar with the project
- **Currency**: Update for latest version and dependencies

### Style Review
- **Consistency**: Follow established patterns and terminology
- **Grammar**: Use proper grammar and spelling
- **Formatting**: Consistent use of markdown and styling
- **Links**: Verify all links work and point to current resources

### User Testing
- **Follow tutorials**: Can new users complete documented workflows?
- **Test examples**: Do all code examples work as shown?
- **Check troubleshooting**: Are solutions effective for common problems?
- **Gather feedback**: Ask users about documentation gaps or confusion
