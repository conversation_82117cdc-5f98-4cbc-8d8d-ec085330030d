{"name": "convert-apps-to-homebrew", "version": "1.0.0", "description": "Convert macOS applications to Homebrew installations with interactive selection", "main": "dist/index.js", "bin": {"convert-apps-to-homebrew": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prepublishOnly": "npm run build && npm test", "start": "node dist/index.js"}, "keywords": ["homebrew", "macos", "applications", "cli", "package-manager", "automation", "typescript", "interactive", "cask", "formula", "installer", "migration", "apps", "brew"], "author": "convert-apps-to-homebrew contributors", "license": "MIT", "engines": {"node": ">=22.0.0"}, "dependencies": {"@inquirer/checkbox": "^4.1.8", "@inquirer/password": "^4.0.15", "commander": "^12.1.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "typescript": "^5.7.2"}, "files": ["dist/**/*", "README.md", "LICENSE", "CHANGELOG.md"], "os": ["darwin"], "repository": {"type": "git", "url": "https://github.com/yourusername/convert-apps-to-homebrew.git"}, "bugs": {"url": "https://github.com/yourusername/convert-apps-to-homebrew/issues"}, "homepage": "https://github.com/yourusername/convert-apps-to-homebrew#readme"}