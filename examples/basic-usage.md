# Basic Usage Examples

This document provides practical examples of using `convert-apps-to-homebrew` in different scenarios.

## Quick Start

### 1. Basic Interactive Mode
```bash
npx convert-apps-to-homebrew
```

**What happens:**
1. Scans `/Applications` directory
2. Shows summary of discoverable apps
3. Presents interactive checkbox with all apps selected
4. Prompts for sudo password if needed
5. Installs selected packages
6. Shows final summary

**Example output:**
```
🍺 convert-apps-to-homebrew v1.0.0

📊 Discovery Summary
══════════════════════════════════════════════════════

✅ Available for installation (5):
   📦 3 casks
   ⚙️  2 formulas

🍺 Already installed via Homebrew (2):
   • Google Chrome
   • Node.js

🎯 Select applications to install via Homebrew:
❯ ◉ Visual Studio Code (📦 cask)
  ◉ Firefox (📦 cask)
  ◉ Slack (📦 cask)
  ◉ Git (⚙️  formula)
  ◉ Python (⚙️  formula)

🔐 Administrator Access Required
══════════════════════════════════════════════════════

The following 3 apps require deleting original .app files:
• Visual Studio Code
• Firefox  
• Slack

This requires administrator privileges to delete files from /Applications.
? Enter your password: [hidden]

📋 Installation Plan
══════════════════════════════════════════════════════

📦 Casks to install (3):
   • Visual Studio Code → visual-studio-code
   • Firefox → firefox
   • Slack → slack
   ✓ Will delete original .app files (sudo access provided)

⚙️  Formulas to install (2):
   • Git → git
   • Python → python
   ℹ️  Original .app files will be kept

🚀 Ready to proceed with installation.

❓ Proceed with installation? (y/N): y

🚀 Starting Package installation (5 items)...
✅ Package installation completed in 45.2s

📊 INSTALLATION SUMMARY
══════════════════════════════════════════════════════
✅ Successfully installed: 5
   • Visual Studio Code (visual-studio-code)
   • Firefox (firefox)
   • Slack (slack)
   • Git (git)
   • Python (python)

🎉 Installation Complete
══════════════════════════════════════════════════════
✅ Successfully installed (5):
• Visual Studio Code
• Firefox
• Slack
• Git
• Python

🍺 Thank you for using convert-apps-to-homebrew!
```

## Advanced Usage Scenarios

### 2. Dry-Run Mode (Preview Only)
```bash
npx convert-apps-to-homebrew --dry-run
```

**Use cases:**
- Preview what would be installed
- Test the tool without making changes
- Understand which apps are available

**Example output:**
```
🔍 DRY RUN SUMMARY
══════════════════════════════════════════════════════
📊 Would have processed 3 apps:
   📦 2 casks
   ⚙️  1 formula

🍺 Thank you for using convert-apps-to-homebrew!
```

### 3. Ignoring Specific Applications
```bash
npx convert-apps-to-homebrew --ignore "Adobe Creative Cloud" "Microsoft Office"
```

**Use cases:**
- Keep certain apps as manual installations
- Avoid apps with complex licensing
- Maintain specific versions

### 4. Verbose Mode for Debugging
```bash
npx convert-apps-to-homebrew --verbose --dry-run
```

**Use cases:**
- Troubleshooting issues
- Understanding the discovery process
- Debugging Homebrew package matching

### 5. Custom Applications Directory
```bash
npx convert-apps-to-homebrew --applications-dir /Users/<USER>/Applications
```

**Use cases:**
- Scanning user-specific application directories
- Testing with a subset of applications
- Custom installation locations

## Common Workflows

### Workflow 1: Safe Exploration
```bash
# Step 1: See what's available (dry-run)
npx convert-apps-to-homebrew --dry-run --verbose

# Step 2: Run with specific ignores if needed
npx convert-apps-to-homebrew --ignore "Expensive Software" "Beta Apps"
```

### Workflow 2: Selective Installation
```bash
# Ignore apps you want to keep as manual installations
npx convert-apps-to-homebrew --ignore "Adobe Photoshop" "Final Cut Pro" "Logic Pro"
```

### Workflow 3: Development Environment Setup
```bash
# Convert development tools to Homebrew
npx convert-apps-to-homebrew --ignore "Games" "Media Apps"
```

## Expected Results by App Type

### Casks (GUI Applications)
- **Original app**: Deleted from `/Applications` (requires sudo)
- **New location**: Managed by Homebrew in `/Applications`
- **Updates**: Via `brew upgrade --cask`
- **Uninstall**: Via `brew uninstall --cask app-name`

### Formulas (Command-line Tools)
- **Original app**: Kept in `/Applications` (if applicable)
- **New location**: Binaries in `/opt/homebrew/bin`
- **Updates**: Via `brew upgrade`
- **Uninstall**: Via `brew uninstall app-name`

## Troubleshooting Common Scenarios

### No Apps Found
```bash
# Check if apps exist
ls -la /Applications

# Try custom directory
npx convert-apps-to-homebrew --applications-dir /Users/<USER>/Applications
```

### Permission Issues
```bash
# Ensure Homebrew is properly installed
brew doctor

# Check /Applications permissions
ls -la /Applications | head -5
```

### Homebrew Not Found
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Add to PATH (follow installation instructions)
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"
```

## Tips for Best Results

1. **Start with dry-run**: Always preview changes first
2. **Use ignore flag**: Exclude apps you want to keep manual
3. **Check Homebrew health**: Run `brew doctor` before starting
4. **Backup important data**: Though the tool is safe, backups are good practice
5. **Update Homebrew**: Run `brew update` for latest package information

## Integration with Existing Workflows

### Dotfiles Integration
```bash
# Add to your dotfiles setup script
if command -v npx >/dev/null 2>&1; then
    echo "Converting apps to Homebrew..."
    npx convert-apps-to-homebrew --ignore "Personal Apps" "Licensed Software"
fi
```

### CI/CD for Team Setup
```bash
# In team setup scripts
npx convert-apps-to-homebrew --dry-run > available-apps.txt
# Review and create team-specific ignore list
```

### Regular Maintenance
```bash
# Monthly check for new convertible apps
npx convert-apps-to-homebrew --dry-run --verbose
```
