# Debugging and Troubleshooting Rules

## Debugging Philosophy

### Systematic Approach
- **Reproduce the issue**: Understand the exact conditions that cause the problem
- **Isolate the problem**: Narrow down to the specific component or function
- **Gather information**: Collect logs, error messages, and system state
- **Form hypotheses**: Develop theories about the root cause
- **Test systematically**: Verify each hypothesis methodically

### Information Gathering
- **Enable verbose logging**: Use `--verbose` flag for detailed output
- **Check system state**: Verify Homebrew installation, permissions, network
- **Review recent changes**: What changed since it last worked?
- **Collect environment info**: Node.js version, macOS version, Homebrew version

## Common Issues and Solutions

### Homebrew-Related Issues

**Issue**: "Homebrew is not installed"
**Debugging Steps**:
1. Check if `brew` command exists: `which brew`
2. Check PATH configuration: `echo $PATH`
3. Verify Homebrew installation: `brew --version`
4. Check shell configuration: `.zshrc`, `.bash_profile`

**Issue**: "Package not found in Homebrew"
**Debugging Steps**:
1. Search manually: `brew search app-name`
2. Check cask vs formula: `brew search --cask app-name`
3. Verify package name normalization
4. Check if package was recently removed from Homebrew

**Issue**: "Permission denied during installation"
**Debugging Steps**:
1. Check Homebrew permissions: `brew doctor`
2. Verify sudo access: `sudo -v`
3. Check /Applications permissions: `ls -la /Applications`
4. Try manual installation: `brew install --cask app-name`

### Application Discovery Issues

**Issue**: "No applications found"
**Debugging Steps**:
1. Verify directory exists: `ls -la /Applications`
2. Check directory permissions: `ls -ld /Applications`
3. Try custom directory: `--applications-dir /Users/<USER>/Applications`
4. Check for hidden files: `ls -la /Applications | grep "^\\."`

**Issue**: "App not detected correctly"
**Debugging Steps**:
1. Check app bundle structure: `ls -la "/Applications/App Name.app"`
2. Verify Info.plist exists: `ls "/Applications/App Name.app/Contents/Info.plist"`
3. Check app name normalization logic
4. Test with simple app names first

### Installation Issues

**Issue**: "Installation fails silently"
**Debugging Steps**:
1. Run with verbose mode: `--verbose`
2. Check brew command manually: `brew install --cask app-name`
3. Verify network connectivity: `ping github.com`
4. Check disk space: `df -h`

**Issue**: "Sudo password not working"
**Debugging Steps**:
1. Test sudo manually: `sudo -v`
2. Check password prompt behavior
3. Verify sudo timeout settings
4. Try without sudo (formulas only)

## Debugging Tools and Techniques

### Logging Strategies
```typescript
// Use structured logging with context
logger.debug('Starting app discovery', { 
  directory: config.applicationsDir,
  ignoredApps: config.ignoredApps.length 
});

// Log function entry/exit for complex operations
logger.verbose(`Entering installApps with ${apps.length} apps`);
try {
  // Operation
  logger.verbose(`Successfully completed installApps`);
} catch (error) {
  logger.error(`installApps failed: ${error.message}`);
  throw error;
}
```

### Error Context Collection
```typescript
// Collect relevant context for errors
function createErrorContext(): ErrorContext {
  return {
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    cwd: process.cwd(),
    timestamp: new Date().toISOString()
  };
}

// Include context in error reports
catch (error) {
  const context = createErrorContext();
  logger.error('Operation failed', { error: error.message, context });
}
```

### Command Debugging
```typescript
// Log commands before execution (but not passwords)
function logCommand(command: string, containsPassword: boolean = false): void {
  if (containsPassword) {
    logger.verbose('Executing sudo command (password hidden)');
  } else {
    logger.verbose(`Executing command: ${command}`);
  }
}

// Capture and log command output
async function executeWithLogging(command: string): Promise<string> {
  logger.debug(`Executing: ${command}`);
  const result = await execAsync(command);
  logger.debug(`Command output: ${result.stdout}`);
  if (result.stderr) {
    logger.debug(`Command stderr: ${result.stderr}`);
  }
  return result.stdout;
}
```

## Testing for Debugging

### Reproduction Test Cases
```typescript
// Create tests that reproduce reported issues
describe('Bug Reproduction', () => {
  it('should handle app names with special characters', async () => {
    const apps = [createMockApp('App-Name (Beta)')];
    const result = await discoverApps(apps);
    expect(result).toBeDefined();
  });

  it('should handle empty Applications directory', async () => {
    mockFs.readdir.mockResolvedValue([]);
    const result = await discoverApps(config);
    expect(result).toEqual([]);
  });
});
```

### Integration Testing for Edge Cases
```typescript
// Test real-world scenarios that commonly fail
describe('Integration Edge Cases', () => {
  it('should handle network timeouts gracefully', async () => {
    // Mock network timeout
    mockExec.mockImplementation(() => {
      throw new Error('ETIMEDOUT');
    });

    await expect(checkBrewAvailability('app')).rejects.toThrow();
  });
});
```

## User Support and Troubleshooting

### Information Collection Script
```typescript
// Collect diagnostic information for user support
export async function collectDiagnostics(): Promise<DiagnosticInfo> {
  return {
    nodeVersion: process.version,
    platform: process.platform,
    homebrewVersion: await getHomebrewVersion(),
    applicationsCount: await countApplications(),
    permissions: await checkPermissions(),
    networkConnectivity: await checkNetworkConnectivity()
  };
}
```

### Self-Diagnostic Features
```typescript
// Built-in diagnostic command
export async function runDiagnostics(): Promise<void> {
  console.log('🔍 Running diagnostics...');
  
  // Check Homebrew
  const brewOk = await checkHomebrew();
  console.log(`Homebrew: ${brewOk ? '✅' : '❌'}`);
  
  // Check permissions
  const permsOk = await checkPermissions();
  console.log(`Permissions: ${permsOk ? '✅' : '❌'}`);
  
  // Check network
  const networkOk = await checkNetwork();
  console.log(`Network: ${networkOk ? '✅' : '❌'}`);
}
```

### Error Recovery Suggestions
```typescript
// Provide specific recovery steps for common errors
function getRecoverySteps(error: ConvertAppsError): string[] {
  switch (error.type) {
    case ErrorType.HOMEBREW_NOT_INSTALLED:
      return [
        'Install Homebrew: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"',
        'Add to PATH: echo \'eval "$(/opt/homebrew/bin/brew shellenv)"\' >> ~/.zprofile',
        'Restart terminal and try again'
      ];
    
    case ErrorType.PERMISSION_DENIED:
      return [
        'Check file permissions: ls -la /Applications',
        'Fix Homebrew permissions: sudo chown -R $(whoami) /opt/homebrew',
        'Verify sudo access: sudo -v'
      ];
    
    default:
      return ['Run with --verbose for more details'];
  }
}
```

## Performance Debugging

### Timing Analysis
```typescript
// Measure operation performance
async function timedOperation<T>(
  name: string, 
  operation: () => Promise<T>
): Promise<T> {
  const start = Date.now();
  try {
    const result = await operation();
    const duration = Date.now() - start;
    logger.debug(`${name} completed in ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    logger.debug(`${name} failed after ${duration}ms`);
    throw error;
  }
}
```

### Memory Usage Monitoring
```typescript
// Monitor memory usage for long operations
function logMemoryUsage(operation: string): void {
  const usage = process.memoryUsage();
  logger.debug(`Memory usage after ${operation}:`, {
    rss: `${Math.round(usage.rss / 1024 / 1024)}MB`,
    heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`
  });
}
```

## Documentation for Debugging

### Troubleshooting Guide Updates
- Keep troubleshooting section current with new issues
- Include specific error messages and solutions
- Provide step-by-step diagnostic procedures
- Link to relevant external resources

### Debug Mode Documentation
- Document all debug flags and their effects
- Explain how to interpret verbose output
- Provide examples of common debug scenarios
- Include information about log file locations
