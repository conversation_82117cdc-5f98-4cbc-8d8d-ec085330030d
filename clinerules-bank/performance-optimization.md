# Performance Optimization Rules

## Performance Priorities

### CLI Responsiveness
- **Startup time**: CLI should start in <2 seconds
- **Interactive response**: User input should be acknowledged within 100ms
- **Progress feedback**: Show progress for operations >2 seconds
- **Graceful degradation**: Handle slow systems appropriately

### Memory Efficiency
- **Streaming data**: Don't load large outputs into memory
- **Resource cleanup**: Clean up resources in finally blocks
- **Efficient data structures**: Use appropriate collections for the task
- **Memory leak prevention**: Monitor for memory leaks in long-running operations

### Network Operations
- **Batch requests**: Combine multiple Homebrew operations when possible
- **Timeout handling**: Set reasonable timeouts for network operations
- **Retry logic**: Implement exponential backoff for transient failures
- **Cache results**: Cache Homebrew package information when appropriate

## Optimization Strategies

### Command Execution
- **Batch brew commands**: Install multiple packages in single command
- **Parallel operations**: Use Promise.all for independent operations
- **Command optimization**: Use most efficient brew command flags
- **Output parsing**: Efficient parsing of command output

### File System Operations
- **Minimize file access**: Cache file system information
- **Efficient directory scanning**: Use appropriate fs methods
- **Path operations**: Use path utilities for cross-platform compatibility
- **Permission checking**: Check permissions before attempting operations

### User Interface Performance
- **Throttled updates**: Limit progress update frequency to 1-second intervals
- **Efficient rendering**: Minimize console output operations
- **Responsive input**: Don't block on user input processing
- **Visual feedback**: Provide immediate feedback for user actions

## Performance Monitoring

### Metrics to Track
- **CLI startup time**: Time from command execution to first output
- **App discovery time**: Time to scan and categorize applications
- **Installation time**: Time for batch installation operations
- **Memory usage**: Peak memory usage during operations

### Performance Testing
- **Large datasets**: Test with 100+ applications
- **Slow networks**: Test with simulated network delays
- **Resource constraints**: Test on systems with limited resources
- **Concurrent operations**: Test multiple simultaneous operations

### Optimization Techniques
- **Profiling**: Use Node.js profiling tools to identify bottlenecks
- **Benchmarking**: Measure performance before and after changes
- **Load testing**: Test with realistic data volumes
- **Memory profiling**: Monitor memory usage patterns

## Code Patterns for Performance

### Async Operations
```typescript
// Prefer Promise.all for independent operations
const [caskResults, formulaResults] = await Promise.all([
  installCasks(casks, config),
  installFormulas(formulas, config)
]);

// Use proper timeout handling
const result = await executeCommand(command, dryRun, 30000); // 30s timeout
```

### Memory Management
```typescript
// Stream large outputs instead of loading into memory
const stream = spawn('brew', ['list', '--cask']);
stream.stdout.on('data', (chunk) => {
  // Process chunk by chunk
});

// Clean up resources
try {
  // Operations
} finally {
  // Cleanup
}
```

### Efficient Data Processing
```typescript
// Use Map for O(1) lookups
const packageMap = new Map(packages.map(pkg => [pkg.name, pkg]));

// Use Set for O(1) membership testing
const ignoredSet = new Set(ignoredApps);

// Avoid unnecessary array operations
const filtered = apps.filter(app => !ignoredSet.has(app.name));
```

## Performance Anti-Patterns to Avoid

### Inefficient Operations
- **Synchronous file operations**: Use async versions
- **Nested loops**: Optimize with better data structures
- **Repeated calculations**: Cache computed values
- **Unnecessary object creation**: Reuse objects when possible

### Memory Leaks
- **Event listener cleanup**: Remove event listeners when done
- **Timer cleanup**: Clear timeouts and intervals
- **Stream cleanup**: Close streams and file handles
- **Reference cycles**: Avoid circular references

### Network Inefficiency
- **Sequential requests**: Use parallel requests when possible
- **No timeout handling**: Always set timeouts for network operations
- **No retry logic**: Implement retry for transient failures
- **Excessive requests**: Batch operations when possible
